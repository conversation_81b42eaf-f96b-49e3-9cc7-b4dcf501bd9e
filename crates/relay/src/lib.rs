mod auth;
pub mod cluster;
mod connection;
pub mod metrics;
pub mod status;

use std::net::{IpAddr, Ipv4Addr, SocketAddr};

use crate::{auth::Auth, connection::Connection};
pub use cluster::*;
use config::Config;

pub struct RelaySever {
    config: Config,
    fingerprints: Vec<String>,
    server: Option<moq_native::Server>,
    client: Option<moq_native::Client>,
    cluster: Option<Cluster>,
    addr: SocketAddr,
    shutdown_rx: Option<tokio::sync::broadcast::Receiver<()>>,
    shutdown_tx: tokio::sync::broadcast::Sender<()>,
}

impl RelaySever {
    pub fn new() -> Self {
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 443);
        let (shutdown_tx, shutdown_rx) = tokio::sync::broadcast::channel(1);

        Self {
            config: Config::default(),
            fingerprints: Vec::new(),
            server: None,
            client: None,
            cluster: None,
            addr,
            shutdown_rx: Some(shutdown_rx),
            shutdown_tx,
        }
    }

    pub async fn create(mut self) -> anyhow::Result<Self> {
        let config = Config::load()?;

        let addr = config
            .relay
            .server
            .listen
            .unwrap_or("[::]:443".parse().unwrap());
        let server = config.relay.server.clone().init()?;
        let client = config.relay.client.clone().init()?;

        self.fingerprints = server.fingerprints().to_vec();

        // Enable node status and configure cluster
        let mut cluster_config = config.relay.cluster.clone();
        cluster_config.node_status_enabled = true;
        cluster_config.max_streams = cluster_config.max_streams.max(100); // Ensure reasonable default

        let cluster = Cluster::new(cluster_config, client.clone());
        let cloned = cluster.clone();
        tokio::spawn(async move { cloned.run().await.expect("cluster failed") });

        self.addr = addr;
        self.config = config;
        self.server = Some(server);
        self.client = Some(client);
        self.cluster = Some(cluster);

        Ok(self)
    }

    pub async fn run(&mut self) -> anyhow::Result<()> {
        let auth = Auth::new(self.config().relay.auth.clone())?;
        tracing::info!(%self.addr, "listening");
        let mut conn_id = 0;
        
        let mut shutdown_rx = self.shutdown_rx.take().expect("shutdown_rx should be present");
        
        loop {
            tokio::select! {
                // Handle new connections
                conn_result = self.server()?.accept() => {
                    match conn_result {
                        Some(conn) => {
                            let token = match auth.validate(conn.url()) {
                                Ok(token) => token,
                                Err(err) => {
                                    tracing::warn!(?err, "failed to validate token");
                                    conn.close(1, b"invalid token");
                                    continue;
                                }
                            };

                            let conn = Connection {
                                id: conn_id,
                                session: conn.into(),
                                cluster: self.cluster()?.clone(),
                                token,
                            };

                            conn_id += 1;
                            tokio::spawn(conn.run());
                        }
                        None => {
                            tracing::info!("Server stopped accepting connections");
                            break;
                        }
                    }
                }
                
                // Handle shutdown signal
                _ = shutdown_rx.recv() => {
                    tracing::info!("Relay server received shutdown signal");
                    // Close the server to stop accepting new connections
                    if let Ok(server) = self.server() {
                        server.close();
                    }
                    break;
                }
            }
        }

        Ok(())
    }

    pub fn fingerprints(&self) -> &[String] {
        &self.fingerprints
    }

    pub fn config(&self) -> &Config {
        &self.config
    }

    pub fn bind_addr(&self) -> SocketAddr {
        self.addr.clone()
    }

    pub fn server(&mut self) -> anyhow::Result<&mut moq_native::Server> {
        match &mut self.server {
            Some(server) => Ok(server),
            None => Err(anyhow::anyhow!("server not started")),
        }
    }

    pub fn client(&self) -> anyhow::Result<&moq_native::Client> {
        match &self.client {
            Some(client) => Ok(client),
            None => Err(anyhow::anyhow!("client not initialized")),
        }
    }

    pub fn cluster(&self) -> anyhow::Result<&Cluster> {
        match &self.cluster {
            Some(cluster) => Ok(cluster),
            None => Err(anyhow::anyhow!("cluster not started")),
        }
    }

    /// Get a handle for graceful shutdown
    pub fn handle(&self) -> RelayServerHandle {
        RelayServerHandle {
            shutdown_tx: self.shutdown_tx.clone(),
        }
    }
}
pub struct RelayServerHandle {
    shutdown_tx: tokio::sync::broadcast::Sender<()>,
}

impl RelayServerHandle {
    pub async fn shutdown(self) -> anyhow::Result<()> {
        tracing::info!("Shutting down relay server...");
        let _ = self.shutdown_tx.send(());
        Ok(())
    }
}
