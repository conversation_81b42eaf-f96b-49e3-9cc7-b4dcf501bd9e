[workspace]
resolver = "2"

# keywords = ["quic", "http3", "webtransport", "media", "live"]
# categories = ["multimedia", "network-programming", "web-programming"]

members = [
    "edify",
    "crates/streamhub",
    "crates/relay",
    "crates/stream_client",
    "crates/config",
]

[workspace.dependencies]
moq-native = { version = "0.7.0" }
moq-lite = { version = "0.3.0", features = ["serde"] }
moq-token = "0.1.0"
web-transport = "0.9.3"

tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
anyhow = { version = "1.0.98", features = ["backtrace"] }
tokio = { version = "1.44.2", features = ["full"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
serde_with = { version = "3", features = ["json", "base64"] }
thiserror = "2.0.12"
uuid = { version = "1.0", features = ["v4"] }

config = { path = "crates/config" }
relay = { path = "crates/relay" }
streamhub = { path = "crates/streamhub" }
stream_client = { path = "crates/stream_client" }
