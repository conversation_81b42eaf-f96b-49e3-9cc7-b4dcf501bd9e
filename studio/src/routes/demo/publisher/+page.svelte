<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import Header from '$lib/components/Header.svelte';
	import StatusBar from '$lib/components/StatusBar.svelte';
	import { StudioPublisher, type PublisherStatus, type StreamDevice, type StreamQuality } from '$lib/services/moq-publisher';
	import { checkSupport, getSupportErrorMessage } from '$lib/services/support';
	import type { SupportCheckResult } from '$lib/types/support';
	import { STREAMING_CONFIG } from '$lib/config/streaming';
	
	// State
	let publisher: StudioPublisher | undefined;
	let status: PublisherStatus | undefined;
	let supportResult: SupportCheckResult | undefined;
	let videoElement: HTMLVideoElement | undefined;
	let videoContainer: HTMLDivElement;
	
	// Controls
	let isStreaming = $state(false);
	let connectionStatus = $state<'connecting' | 'connected' | 'disconnected' | 'unsupported'>('disconnected');
	let selectedDevice = $state<StreamDevice>('camera');
	let selectedQuality = $state<StreamQuality>('HD');
	let streamPath = $state('demo/publisher');
	
	// Logs
	let logs = $state<string[]>([]);
	
	function addLog(message: string) {
		const timestamp = new Date().toLocaleTimeString();
		logs = [`[${timestamp}] ${message}`, ...logs.slice(0, 49)]; // Keep last 50 logs
	}
	
	async function initializePublisher() {
		try {
			addLog('Initializing publisher...');
			
			publisher = new StudioPublisher({
				relayUrl: STREAMING_CONFIG.relay.url,
				streamPath,
				device: selectedDevice,
				audioEnabled: true,
				videoEnabled: true
			});
			
			// Subscribe to status changes
			publisher.onStatusChange((newStatus) => {
				status = newStatus;
				isStreaming = newStatus.isPublishing;
				connectionStatus = newStatus.connectionStatus;
				
				addLog(`Status: ${newStatus.connectionStatus}, Publishing: ${newStatus.isPublishing}`);
				
				// Update video preview
				updateVideoPreview();
			});
			
			addLog('Publisher initialized successfully');
		} catch (error) {
			addLog(`Failed to initialize publisher: ${error}`);
			console.error(error);
		}
	}
	
	function updateVideoPreview() {
		if (videoElement) {
			videoElement.remove();
			videoElement = undefined;
		}
		
		const newVideo = publisher?.getVideoElement();
		if (newVideo && videoContainer) {
			videoElement = newVideo;
			videoElement.className = 'w-full h-full object-cover rounded-lg';
			videoContainer.appendChild(videoElement);
			addLog('Video preview updated');
		}
	}
	
	async function startStream() {
		if (!publisher) {
			addLog('Publisher not initialized');
			return;
		}
		
		try {
			addLog(`Starting stream: ${streamPath} with ${selectedDevice}`);
			await publisher.startStream(streamPath, selectedDevice);
		} catch (error) {
			addLog(`Failed to start stream: ${error}`);
		}
	}
	
	async function stopStream() {
		if (!publisher) return;
		
		try {
			addLog('Stopping stream...');
			await publisher.stopStream();
		} catch (error) {
			addLog(`Failed to stop stream: ${error}`);
		}
	}
	
	function changeDevice() {
		if (publisher) {
			publisher.setDevice(selectedDevice);
			addLog(`Device changed to: ${selectedDevice}`);
		}
	}
	
	function changeQuality() {
		if (publisher) {
			publisher.setQuality(selectedQuality);
			addLog(`Quality changed to: ${selectedQuality}`);
		}
	}
	
	function toggleAudio() {
		if (publisher) {
			const enabled = !status?.hasAudio;
			publisher.setAudioEnabled(enabled);
			addLog(`Audio ${enabled ? 'enabled' : 'disabled'}`);
		}
	}
	
	function toggleVideo() {
		if (publisher) {
			const enabled = !status?.hasVideo;
			publisher.setVideoEnabled(enabled);
			addLog(`Video ${enabled ? 'enabled' : 'disabled'}`);
		}
	}
	
	function clearLogs() {
		logs = [];
	}
	
	onMount(async () => {
		addLog('Checking browser support...');
		
		try {
			supportResult = await checkSupport('publish');
			addLog(`Support level: ${supportResult.overall}`);
			
			if (supportResult.overall === 'none') {
				addLog(`Error: ${getSupportErrorMessage(supportResult)}`);
				return;
			}
			
			await initializePublisher();
		} catch (error) {
			addLog(`Support check failed: ${error}`);
		}
	});
	
	onDestroy(() => {
		publisher?.destroy();
	});
</script>

<svelte:head>
	<title>MoQ Publisher Demo - Edify Studio</title>
</svelte:head>

<div class="w-full min-h-screen bg-base-200 flex flex-col">
	<!-- Main Content -->
	<div class="container mx-auto p-4 max-w-6xl flex-1">
		<div class="flex items-center gap-4 mb-6">
			<a href="/" class="btn btn-ghost">← Back to Studio</a>
			<h1 class="text-3xl font-bold">MoQ Publisher Demo</h1>
			<div class="badge {supportResult?.overall === 'full' ? 'badge-success' : supportResult?.overall === 'partial' ? 'badge-warning' : 'badge-error'}">
				{supportResult?.overall || 'checking...'}
			</div>
		</div>
		
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Video Preview -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">Video Preview</h2>
					<div class="relative w-full aspect-video bg-base-300 rounded-lg overflow-hidden" bind:this={videoContainer}>
						{#if !videoElement}
							<div class="absolute inset-0 flex items-center justify-center text-base-content/60">
								<div class="text-center">
									<div class="text-6xl mb-4">📹</div>
									<p>No video preview</p>
									<p class="text-sm">Start streaming to see preview</p>
								</div>
							</div>
						{/if}
					</div>
					
					<!-- Status indicators -->
					<div class="flex gap-2 mt-4">
						<div class="badge {connectionStatus === 'connected' ? 'badge-success' : connectionStatus === 'connecting' ? 'badge-warning' : 'badge-error'}">
							{connectionStatus}
						</div>
						{#if isStreaming}
							<div class="badge badge-error">LIVE</div>
						{/if}
						{#if status?.hasAudio}
							<div class="badge badge-info">🎤 Audio</div>
						{/if}
						{#if status?.hasVideo}
							<div class="badge badge-info">📹 Video</div>
						{/if}
					</div>
				</div>
			</div>
			
			<!-- Controls -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">Publisher Controls</h2>
					
					<!-- Stream Path -->
					<div class="form-control">
						<label class="label">
							<span class="label-text">Stream Path</span>
						</label>
						<input type="text" bind:value={streamPath} class="input input-bordered" disabled={isStreaming} />
					</div>
					
					<!-- Device Selection -->
					<div class="form-control">
						<label class="label">
							<span class="label-text">Device</span>
						</label>
						<select bind:value={selectedDevice} onchange={changeDevice} class="select select-bordered" disabled={isStreaming}>
							<option value="camera">Camera</option>
							<option value="screen">Screen Share</option>
						</select>
					</div>
					
					<!-- Quality Selection -->
					<div class="form-control">
						<label class="label">
							<span class="label-text">Quality</span>
						</label>
						<select bind:value={selectedQuality} onchange={changeQuality} class="select select-bordered">
							<option value="4K">4K (3840x2160)</option>
							<option value="HD">HD (1920x1080)</option>
							<option value="SD">SD (1280x720)</option>
						</select>
					</div>
					
					<!-- Stream Controls -->
					<div class="flex gap-2 mt-4">
						{#if !isStreaming}
							<button class="btn btn-success flex-1" onclick={startStream} disabled={connectionStatus !== 'connected' && connectionStatus !== 'disconnected'}>
								Start Stream
							</button>
						{:else}
							<button class="btn btn-error flex-1" onclick={stopStream}>
								Stop Stream
							</button>
						{/if}
					</div>
					
					<!-- Media Controls -->
					{#if isStreaming}
						<div class="flex gap-2">
							<button class="btn btn-outline flex-1" onclick={toggleAudio}>
								{status?.hasAudio ? '🔇 Mute' : '🔊 Unmute'}
							</button>
							<button class="btn btn-outline flex-1" onclick={toggleVideo}>
								{status?.hasVideo ? '📹 Disable Video' : '📹 Enable Video'}
							</button>
						</div>
					{/if}
				</div>
			</div>
		</div>
		
		<!-- Logs -->
		<div class="card bg-base-100 shadow-xl mt-6">
			<div class="card-body">
				<div class="flex items-center justify-between">
					<h2 class="card-title">Event Logs</h2>
					<button class="btn btn-ghost btn-sm" onclick={clearLogs}>Clear</button>
				</div>
				<div class="bg-base-300 rounded p-4 h-48 overflow-y-auto font-mono text-sm">
					{#each logs as log}
						<div class="mb-1">{log}</div>
					{/each}
					{#if logs.length === 0}
						<div class="text-base-content/60">No logs yet...</div>
					{/if}
				</div>
			</div>
		</div>
		
		<!-- Support Details -->
		{#if supportResult}
			<div class="card bg-base-100 shadow-xl mt-6">
				<div class="card-body">
					<h2 class="card-title">Browser Support Details</h2>
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div class="stat">
							<div class="stat-title">Overall</div>
							<div class="stat-value text-lg {supportResult.overall === 'full' ? 'text-success' : supportResult.overall === 'partial' ? 'text-warning' : 'text-error'}">
								{supportResult.overall}
							</div>
						</div>
						<div class="stat">
							<div class="stat-title">WebTransport</div>
							<div class="stat-value text-lg {supportResult.details.webtransport ? 'text-success' : 'text-error'}">
								{supportResult.details.webtransport ? 'Yes' : 'No'}
							</div>
						</div>
						<div class="stat">
							<div class="stat-title">Publishing</div>
							<div class="stat-value text-lg {supportResult.publish === 'full' ? 'text-success' : supportResult.publish === 'partial' ? 'text-warning' : 'text-error'}">
								{supportResult.publish}
							</div>
						</div>
					</div>
					
					{#if supportResult.warnings.length > 0}
						<div class="alert alert-warning mt-4">
							<h3 class="font-bold">Warnings:</h3>
							<ul>
								{#each supportResult.warnings as warning}
									<li>• {warning}</li>
								{/each}
							</ul>
						</div>
					{/if}
					
					{#if supportResult.recommendations.length > 0}
						<div class="alert alert-info mt-4">
							<h3 class="font-bold">Recommendations:</h3>
							<ul>
								{#each supportResult.recommendations as rec}
									<li>• {rec}</li>
								{/each}
							</ul>
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
</div>