<script lang="ts">
    // Example state
    let currentSceneId = 457164;
    const scenes = [
      {
        id: 457164,
        name: 'Break',
        thumbnail: 'https://files.evmux.com/121215/scenes/sc_tmb_4571641749801695.jpg?Expires=1750013765&Signature=NSLhAEZ-aCdgZUYFJJTb2NJ6FsEnZL9Gd2DLGxXBDNgSR-cmPVqiiH5zN3Iu88cFofq~Id3TxQBUdT0igdEHN2Ns1vczo9-~etYeiGyICrTJMvv~2C4-kmS4y~0U~0voHykMvxplTflIS7gMJBXuJ3oJOEDd2WxWp-VVA0Lu6~nyk4Bye7OCIjgZBvyiUAfnbLaP0kEiZcTnJ6UVPyAV81gEX1pkMEAIVItml3kkOFKyo8EHYPZQ~PXvB3Nvg9JkiRdCrNceqwBhG-O7QWmvgaMlHeF9bM8xc27wvJqrVqEm~eZRy0v6-T4UqvtENFw8z7COboUdCsRQBWSf43M4aQ__&Key-Pair-Id=APKAJQZJ2AA6OYF4WJHA'
      },
      {
        id: 457165,
        name: 'Tech Talk',
        thumbnail: 'https://files.evmux.com/121215/scenes/sc_tmb_4571651749801696.jpg?Expires=1750013765&Signature=eUsdzsIdZ~fEKaAUnXDtg~hRJ4UGUR2T0NGcOXMTrxJ4mSRAwd3ioYUfuFg0EllDabWlqSnaDIUociJ1EMzkY5PrIUUTySNpwUta17jPXWTIu2tXV~lcV~LFuvRAhMpe0Lejw28KOzvyw6jABBUXqTY1UmupUaWc-5FggLQxrsdMhpV~NVUiZ8KxzpHysufGaTPx~8SvmhTt5ljnM0pfXfHWBhgmC~fOXZftv7grKJLnUy0hxVL04zSrcMtOoxWIbHxpBT8uIJ~uVYnoeIHicUxnbO3pc1MNFMBTzAZx0py6bADBbjMYKIPOqQjetge6uebDJFx49mv-CAz~fwfsXw__&Key-Pair-Id=APKAJQZJ2AA6OYF4WJHA'
      }
    ];
  
    function selectScene(id: number) {
      currentSceneId = id;
    }
  </script>
  
  <div class="aside" 
       style="--style-version: light; flex: 0 0 162px;"
       bind:this={asideElement}>
    <div class="aside_inner" style="width: 140px;">
      
      <!-- Dynamic Scene Section -->
      <div class="dynamic_scene">
        <h3>
          Dynamic
          <span style="white-space: nowrap;">
            Scene
            <i class="i_question_round" style="position: relative; top: 1px;"></i>
          </span>
        </h3>
        <div class="dynamic_scene_img_wr">
          <div class="dynamic_scene_thumb_svg_wr">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 161 91" class="dynamic_scene_thumb">
              <rect x="0.5" y="0.5" width="160" height="90" class="dynamic_scene_thumb_border_block"/>
              <g class="t_2">
                <path d="M103.5,25.5a7,7,0,0,1,7,7v26a7,7,0,0,1-7,7h-46a7,7,0,0,1-7-7v-26a7,7,0,0,1,7-7h46m0-1h-46a8,8,0,0,0-8,8v26a8,8,0,0,0,8,8h46a8,8,0,0,0,8-8v-26a8,8,0,0,0-8-8Z" class="border_block"/>
                <rect x="82.18" y="36.01" width="24.13" height="18.97" rx="4" class="fig"/>
                <rect x="54.69" y="36.01" width="24.13" height="18.97" rx="4" class="fig"/>
              </g>
            </svg>
          </div>
          <b class="for_reader" style="pointer-events: all;">Select dynamic scene</b>
        </div>
      </div>
  
      <!-- Pro Scenes Section -->
      <div class="pro_scenes">
        <h3>
          Pro
          <span style="white-space: nowrap;">
            Scenes
            <i class="i_question_round" style="position: relative; top: 1px;"></i>
          </span>
        </h3>
        <div id="sceneList" class="ds_scroller scroll_bar_style" style="overflow-y: scroll; margin-bottom: 12px;">
          {#each scenes as scene} 
            <div class="drag_sort_item">
              <div class="drag_sort_item_borderer" style="width: 116px; height: 90.75px;">
                <div class="drag_sort_item_inner" style="width: 116px; height: 90.75px;">
                  <div class="scene_item {current_scene_in_editor if scene.id === currentSceneId}" on:click={() => selectScene(scene.id)}>
                    <div class="click_catcher">
                      <b class="for_reader">Click to select pro scene named {scene.name}</b>
                    </div>
                    <i class="i_drag drag_trigger"></i>
                    <div class="scene_item_options_btn">
                      <i class="i_dots_v"></i>
                    </div>
                    <div class="img_wr">
                      <img id={`scene_thumb_${scene.id}`}
                           draggable={false}
                           src={scene.thumbnail} />
                    </div>
                    <div class="scene_info">
                      <span class="aside_name_scene">{scene.name}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>
        <div class="add_new_scene_btn_wr">
          <span class="add_new_scene_btn">
            <i class="i_plus"></i>
            Add Scene
          </span>
        </div>
      </div>
  
      <div class="aside_dragger"></div>
    </div>
  </div>
  
  <style>
    /* Optional styles can go here or in your global CSS */
    .current_scene_in_editor {
      border: 2px solid #007acc;
      background-color: #f0f8ff;
    }
  
    .scene_item:hover {
      cursor: pointer;
      background-color: #f0f0f0;
    }
  </style>