<script lang="ts">
	import Header from '$lib/components/Header.svelte';
	import Sidebar from '$lib/components/Sidebar.svelte';
	import Controls from '$lib/components/Controls.svelte';
	import Preview from '$lib/components/Preview.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import StatusBar from '$lib/components/StatusBar.svelte';
	import { streamingStore } from '$lib/stores/streaming.svelte';
	import { onMount } from 'svelte';
	
	// Import development tools in dev mode
	if (import.meta.env.DEV) {
		import('$lib/utils/development');
	}
	
	// Set up streaming timer using $effect (runs in component context)
	$effect(() => {
		streamingStore.setupTimer();
	});
	
	onMount(async () => {
		try {
			await streamingStore.initializePublisher('camera');
			
			// Show development info in console
			if (import.meta.env.DEV) {
				console.log('🎬 Edify Studio loaded in development mode');
				console.log('📡 MoQ Relay URL:', import.meta.env.VITE_MOQ_RELAY_URL || 'http://localhost:4443');
				console.log('🛠️ Development tools available as "dev" (try dev.printHelp())');
			}
		} catch (error) {
			console.error('Failed to initialize publisher:', error);
		}
	});
</script>

<svelte:head>
	<title>Edify Studio - Professional Streaming Platform</title>
	<meta name="description" content="Professional streaming studio built with SvelteKit and MoQ" />
</svelte:head>

<div class="w-full h-screen bg-base-200 flex flex-col">
	<Header />
	
	<!-- Main Content Area -->
	<div class="flex flex-1 p-2 gap-4 ml-1">
		<Sidebar />
		<div class="flex flex-1 gap-4">
			<Controls />
			<Preview />
		</div>
	</div>
	
	<!-- Footer -->
	<Footer />
	
	<!-- Bottom Status Bar (VS Code style) -->
	<StatusBar />
</div>

<style>
	:global(html) {
		height: 100%;
	}
	
	:global(body) {
		min-height: 100vh;
		margin: 0;
	}
</style>
