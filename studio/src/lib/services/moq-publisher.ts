import { Connection } from '@kixelated/hang';
import { Broadcast } from '@kixelated/hang/publish';
import type { 
	PublisherConfig, 
	PublisherStatus, 
	StreamDevice, 
	StreamQuality,
	ConnectionStatus 
} from '$lib/types/streaming';
import { QUALITY_SETTINGS, CONNECTION_CONFIG } from '$lib/constants';

export type { PublisherConfig, PublisherStatus, StreamDevice, StreamQuality };

export class StudioPublisher {
	private connection: Connection;
	private broadcast: Broadcast;
	private config: PublisherConfig;
	private statusCallbacks: Set<(status: PublisherStatus) => void> = new Set();

	constructor(config: PublisherConfig) {
		this.config = config;
		
		// Initialize connection
		this.connection = new Connection({
			url: new URL(config.relayUrl),
			reload: true,
			delay: CONNECTION_CONFIG.retryDelay,
			maxDelay: CONNECTION_CONFIG.maxRetryDelay
		});

		// Initialize broadcast
		this.broadcast = new Broadcast(this.connection, {
			enabled: false,
			path: config.streamPath || '',
			device: config.device || 'camera',
			audio: config.audioEnabled ?? true,
			video: config.videoEnabled ?? true
		});

		this.setupStatusMonitoring();
	}

	private setupStatusMonitoring() {
		// Monitor connection status changes
		this.connection.status.subscribe((status: ConnectionStatus) => {
			this.notifyStatusChange();
		});

		// Monitor broadcast publishing status
		this.broadcast.published.subscribe((isPublishing: boolean) => {
			this.notifyStatusChange();
		});

		// Monitor audio/video media availability
		this.broadcast.audio.media.subscribe(() => {
			this.notifyStatusChange();
		});

		this.broadcast.video.media.subscribe(() => {
			this.notifyStatusChange();
		});
	}

	private notifyStatusChange() {
		const status: PublisherStatus = {
			connectionStatus: this.connection.status.get(),
			isPublishing: this.broadcast.published.get(),
			hasAudio: !!this.broadcast.audio.media.get(),
			hasVideo: !!this.broadcast.video.media.get(),
			streamPath: this.broadcast.path.get()
		};

		this.statusCallbacks.forEach(callback => callback(status));
	}

	// Public API methods
	async startStream(streamPath: string, device: StreamDevice = 'camera'): Promise<void> {
		try {
			this.broadcast.path.set(streamPath);
			this.broadcast.device.set(device);
			this.broadcast.enabled.set(true);
		} catch (error) {
			console.error('Failed to start stream:', error);
			throw error;
		}
	}

	async stopStream(): Promise<void> {
		try {
			this.broadcast.enabled.set(false);
		} catch (error) {
			console.error('Failed to stop stream:', error);
			throw error;
		}
	}

	setAudioEnabled(enabled: boolean): void {
		this.broadcast.audio.constraints.set(enabled);
	}

	setVideoEnabled(enabled: boolean): void {
		this.broadcast.video.constraints.set(enabled);
	}

	setDevice(device: StreamDevice): void {
		this.broadcast.device.set(device);
	}

	setStreamPath(path: string): void {
		this.broadcast.path.set(path);
	}

	setQuality(quality: StreamQuality): void {
		const settings = QUALITY_SETTINGS[quality];
		const constraints = {
			width: { ideal: settings.width },
			height: { ideal: settings.height },
			frameRate: { ideal: settings.frameRate }
		};
		this.broadcast.video.constraints.set(constraints);
	}

	// Status monitoring
	onStatusChange(callback: (status: PublisherStatus) => void): () => void {
		this.statusCallbacks.add(callback);
		
		// Call immediately with current status
		callback({
			connectionStatus: this.connection.status.get(),
			isPublishing: this.broadcast.published.get(),
			hasAudio: !!this.broadcast.audio.media.get(),
			hasVideo: !!this.broadcast.video.media.get(),
			streamPath: this.broadcast.path.get()
		});

		// Return unsubscribe function
		return () => {
			this.statusCallbacks.delete(callback);
		};
	}

	// Get current status
	getStatus(): PublisherStatus {
		return {
			connectionStatus: this.connection.status.get(),
			isPublishing: this.broadcast.published.get(),
			hasAudio: !!this.broadcast.audio.media.get(),
			hasVideo: !!this.broadcast.video.media.get(),
			streamPath: this.broadcast.path.get()
		};
	}

	// Get video element for preview (if available)
	getVideoElement(): HTMLVideoElement | undefined {
		const videoTrack = this.broadcast.video.media.get();
		if (!videoTrack) return undefined;

		const video = document.createElement('video');
		video.srcObject = new MediaStream([videoTrack]);
		video.muted = true;
		video.autoplay = true;
		return video;
	}

	// Cleanup
	destroy(): void {
		this.broadcast.close();
		this.connection.close();
		this.statusCallbacks.clear();
	}
}