// Global constants for Edify Studio

// Codec identifiers for browser support testing
export const CODEC_STRINGS = {
	aac: "mp4a.40.2",
	opus: "opus",
	av1: "av01.0.08M.08",
	h264: "avc1.640028",
	h265: "hev1.1.6.L93.B0",
	vp9: "vp09.00.10.08",
	vp8: "vp8",
} as const;

// Stream quality configurations
export const QUALITY_SETTINGS = {
	'4K': {
		width: 3840,
		height: 2160,
		frameRate: 30,
		bitrate: 15000000, // 15 Mbps
	},
	'HD': {
		width: 1920,
		height: 1080,
		frameRate: 30,
		bitrate: 5000000, // 5 Mbps
	},
	'SD': {
		width: 1280,
		height: 720,
		frameRate: 30,
		bitrate: 2500000, // 2.5 Mbps
	},
} as const;

// Audio settings
export const AUDIO_SETTINGS = {
	sampleRate: 48000,
	numberOfChannels: 2,
	bitrate: 128000, // 128 kbps
} as const;

// Connection timeouts and retry settings
export const CONNECTION_CONFIG = {
	defaultTimeout: 10000, // 10 seconds
	retryDelay: 1000, // 1 second
	maxRetryDelay: 30000, // 30 seconds
	maxRetries: 5,
} as const;

// GOP (Group of Pictures) settings for video encoding
export const VIDEO_GOP = {
	duration: 2000000, // 2 seconds in microseconds
	keyFrameInterval: 2.0, // 2 seconds
} as const;

// Audio GOP settings
export const AUDIO_GOP = {
	duration: 500000, // 0.5 seconds in microseconds
	keyFrameInterval: 0.5, // 0.5 seconds
} as const;

// Default stream paths
export const STREAM_PATHS = {
	host: 'studio/host',
	guestPrefix: 'studio/guest',
	catalog: 'catalog.json',
} as const;

// UI constants
export const UI_CONFIG = {
	statusUpdateInterval: 5000, // 5 seconds
	previewUpdateInterval: 1000, // 1 second
	participantThumbnailSize: 80, // pixels
	maxParticipants: 10,
} as const;

// Browser detection
export const BROWSER_INFO = {
	isFirefox: navigator.userAgent.toLowerCase().includes('firefox'),
	isChrome: navigator.userAgent.toLowerCase().includes('chrome'),
	isSafari: navigator.userAgent.toLowerCase().includes('safari'),
} as const;

// WebTransport and WebCodecs feature detection
export const FEATURE_SUPPORT = {
	webTransport: typeof WebTransport !== 'undefined',
	webCodecs: typeof VideoEncoder !== 'undefined' && typeof AudioEncoder !== 'undefined',
	mediaStreamTrackProcessor: typeof (globalThis as any).MediaStreamTrackProcessor !== 'undefined',
	audioWorklet: typeof AudioWorkletNode !== 'undefined',
	offscreenCanvas: typeof OffscreenCanvas !== 'undefined',
} as const;

// Error messages
export const ERROR_MESSAGES = {
	webTransportNotSupported: 'WebTransport is not supported in this browser. Please use Chrome 97+ or Firefox 114+.',
	webCodecsNotSupported: 'WebCodecs is not supported in this browser. Please use Chrome 94+ or Firefox 103+.',
	mediaPermissionDenied: 'Camera/microphone access denied. Please allow media permissions and try again.',
	connectionFailed: 'Failed to connect to streaming server. Please check your connection and try again.',
	streamingFailed: 'Failed to start streaming. Please check your media devices and try again.',
	codecNotSupported: 'Required audio/video codecs are not supported in this browser.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
	connectionEstablished: 'Successfully connected to streaming server',
	streamStarted: 'Stream started successfully',
	streamStopped: 'Stream stopped successfully',
	guestInvited: 'Guest invitation sent successfully',
	participantJoined: 'Participant joined the session',
} as const;

// Development constants
export const DEV_CONFIG = {
	mockParticipantCount: 3,
	mockViewerCountMin: 1000,
	mockViewerCountMax: 10000,
	debugLogging: true,
} as const;

// API endpoints (for future backend integration)
export const API_ENDPOINTS = {
	invite: '/api/invite',
	participants: '/api/participants',
	streams: '/api/streams',
	health: '/api/health',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
	userPreferences: 'edify-studio-preferences',
	lastStreamQuality: 'edify-studio-last-quality',
	deviceSelection: 'edify-studio-device-selection',
} as const;