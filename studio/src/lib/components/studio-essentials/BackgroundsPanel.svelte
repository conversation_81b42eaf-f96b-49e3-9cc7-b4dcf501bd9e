<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Plus, MoreHorizontal, Image, Play } from 'lucide-svelte';
  
  let { backgrounds } = $derived(studioEssentialsStore);
  
  function setBackground(background: any) {
    studioEssentialsStore.setBackground(background);
  }
  
  function handleAddBackground() {
    console.log('Adding new background...');
    // TODO: Implement background upload
  }
  
  function editBackground(background: any) {
    console.log('Editing background:', background.name);
    // TODO: Implement background editing
  }
  
  function deleteBackground(background: any) {
    if (confirm(`Delete background "${background.name}"?`)) {
      console.log('Deleting background:', background.name);
      // TODO: Implement background deletion
    }
  }
  
  function duplicateBackground(background: any) {
    console.log('Duplicating background:', background.name);
    // TODO: Implement background duplication
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Backgrounds</h3>
    <button 
      class="btn btn-sm btn-ghost btn-square tooltip" 
      data-tip="Add background"
      on:click={handleAddBackground}>
      <Plus size="16" />
    </button>
  </div>
  
  <!-- Background List -->
  <div class="space-y-3">
    {#each backgrounds as background}
      <div class="relative group">
        <button 
          class="w-full aspect-video bg-base-200 rounded overflow-hidden border-2 transition-all hover:scale-[1.02] focus:scale-[1.02]"
          class:border-primary={background.isActive}
          class:border-base-300={!background.isActive}
          class:shadow-md={background.isActive}
          class:ring-2={background.isActive}
          class:ring-primary={background.isActive}
          class:ring-opacity-50={background.isActive}
          on:click={() => setBackground(background)}>
          
          <img 
            src={background.thumbnail} 
            alt={background.name} 
            class="w-full h-full object-cover transition-opacity"
            class:opacity-100={background.isActive}
            class:opacity-70={!background.isActive} />
          
          <!-- Active indicator -->
          {#if background.isActive}
            <div class="absolute inset-0 bg-primary/30 flex items-center justify-center">
              <div class="badge badge-primary badge-lg font-bold">ACTIVE</div>
            </div>
          {/if}
          
          <!-- Background type indicator -->
          <div class="absolute top-2 left-2">
            <div class="badge badge-xs badge-outline bg-base-100/90 text-base-content">
              {#if background.type === 'video'}
                <Play size="10" class="mr-1" />
                Video
              {:else}
                <Image size="10" class="mr-1" />
                Image
              {/if}
            </div>
          </div>
          
          <!-- Hover overlay -->
          <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <div class="text-white text-sm font-medium">
              {background.isActive ? 'Active Background' : 'Click to Activate'}
            </div>
          </div>
        </button>
        
        <div class="flex justify-between items-center mt-2">
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium truncate">{background.name}</p>
            <p class="text-xs text-base-content/60 capitalize">{background.type}</p>
          </div>
          
          <!-- More options -->
          <div class="dropdown dropdown-end">
            <button 
              class="btn btn-ghost btn-xs btn-square opacity-0 group-hover:opacity-100 transition-opacity"
              tabindex="0">
              <MoreHorizontal size="12" />
            </button>
            <ul class="dropdown-content menu bg-base-100 rounded-box w-32 p-2 shadow-lg border border-base-200">
              <li>
                <button class="text-xs" on:click|stopPropagation={() => editBackground(background)}>
                  Edit
                </button>
              </li>
              <li>
                <button class="text-xs" on:click|stopPropagation={() => duplicateBackground(background)}>
                  Duplicate
                </button>
              </li>
              <li>
                <button 
                  class="text-xs text-error" 
                  disabled={background.isActive}
                  on:click|stopPropagation={() => deleteBackground(background)}>
                  Delete
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    {/each}
  </div>
  
  <!-- Empty state -->
  {#if backgrounds.length === 0}
    <div class="text-center py-8">
      <div class="text-base-content/40 mb-2">
        <Image size="32" class="mx-auto" />
      </div>
      <p class="text-xs text-base-content/60 mb-2">No backgrounds yet</p>
      <p class="text-xs text-base-content/40 mb-3">Add backgrounds to customize your stream</p>
      <button class="btn btn-sm btn-primary" on:click={handleAddBackground}>
        <Plus size="14" />
        Add Background
      </button>
    </div>
  {/if}
  
  <!-- Quick Actions -->
  {#if backgrounds.length > 0}
    <div class="flex gap-2 pt-2 border-t border-base-200">
      <button 
        class="btn btn-xs btn-outline flex-1"
        on:click={handleAddBackground}>
        <Plus size="12" />
        Add More
      </button>
    </div>
  {/if}
  
  <!-- Active background info -->
  {#if studioEssentialsStore.activeBackground}
    <div class="alert alert-success text-xs">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-4 h-4">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span>
        "{studioEssentialsStore.activeBackground.name}" is active
      </span>
    </div>
  {/if}
</div>