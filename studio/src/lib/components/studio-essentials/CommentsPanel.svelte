<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Settings, MessageCircle, Pause, Play, Trash2 } from 'lucide-svelte';
  
  let { comments, selectedCommentStyle, commentStyles, brandSettings, commentsPaused } = $derived(studioEssentialsStore);
  let showSettings = $state(false);
  
  function clearComments() {
    studioEssentialsStore.clearComments();
  }
  
  function pauseComments() {
    studioEssentialsStore.toggleCommentsPause();
  }
  
  function setCommentStyle(styleIndex: number) {
    studioEssentialsStore.setCommentStyle(styleIndex);
  }
  
  function formatTimestamp(timestamp: string): string {
    return timestamp;
  }
  
  function getPlatformBadgeColor(platform: string): string {
    const colors = {
      youtube: 'badge-error',
      twitch: 'badge-secondary', 
      facebook: 'badge-info',
      linkedin: 'badge-primary'
    };
    return colors[platform as keyof typeof colors] || 'badge-neutral';
  }
  
  function getCommentStyleClasses(styleIndex: number) {
    const styles = [
      'rounded-lg bg-opacity-90',
      'rounded-2xl bg-opacity-80 animate-pulse',
      'rounded bg-opacity-95',
      'rounded-3xl bg-opacity-75'
    ];
    return styles[styleIndex] || styles[0];
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Comments</h3>
    <div class="flex items-center gap-1">
      <!-- Pause/Resume -->
      <button 
        class="btn btn-xs btn-ghost btn-square tooltip" 
        class:text-warning={commentsPaused}
        data-tip={commentsPaused ? 'Resume comments' : 'Pause comments'}
        on:click={pauseComments}>
        {#if commentsPaused}
          <Play size="14" />
        {:else}
          <Pause size="14" />
        {/if}
      </button>
      
      <!-- Settings -->
      <div class="dropdown dropdown-end">
        <button 
          class="btn btn-xs btn-ghost btn-square tooltip" 
          tabindex="0"
          data-tip="Comment settings">
          <Settings size="14" />
        </button>
        <div class="dropdown-content menu bg-base-100 rounded-box w-64 p-3 shadow-lg border border-base-200 z-50">
          <!-- Style Selection -->
          <div class="form-control mb-3">
            <label class="label">
              <span class="label-text text-xs font-semibold">Comment Style</span>
            </label>
            <div class="grid grid-cols-2 gap-2">
              {#each commentStyles as style, index}
                <button 
                  class="btn btn-xs"
                  class:btn-primary={selectedCommentStyle === index}
                  class:btn-outline={selectedCommentStyle !== index}
                  on:click={() => setCommentStyle(index)}>
                  Style {index + 1}
                </button>
              {/each}
            </div>
          </div>
          
          <!-- Live Preview -->
          <div class="form-control">
            <label class="label">
              <span class="label-text text-xs font-semibold">Preview</span>
            </label>
            <div class="bg-base-200 rounded p-2">
              <div 
                class="inline-block px-2 py-1 text-xs {getCommentStyleClasses(selectedCommentStyle)}"
                style="
                  background-color: {brandSettings.colors.primary}; 
                  color: {brandSettings.colors.secondary};
                  border-radius: {commentStyles[selectedCommentStyle]?.borderRadius || '8px'};
                ">
                Sample comment text
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Status Bar -->
  {#if commentsPaused}
    <div class="alert alert-warning text-xs">
      <Pause size="16" />
      <span>Comments are paused</span>
    </div>
  {/if}
  
  <!-- Comment Feed -->
  <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
    {#each comments as comment}
      <div class="chat chat-start">
        <div class="chat-image avatar">
          <div class="w-6 rounded-full bg-base-300">
            {#if comment.avatar}
              <img src={comment.avatar} alt={comment.author} class="w-full h-full object-cover" />
            {:else}
              <div class="w-full h-full flex items-center justify-center text-xs font-bold">
                {comment.author.charAt(0).toUpperCase()}
              </div>
            {/if}
          </div>
        </div>
        <div class="chat-header text-xs flex items-center gap-2">
          <span class="font-medium">{comment.author}</span>
          <div class="badge {getPlatformBadgeColor(comment.platform)} badge-xs">
            {comment.platform}
          </div>
          <time class="text-xs opacity-50">{formatTimestamp(comment.timestamp)}</time>
        </div>
        <div 
          class="chat-bubble text-xs max-w-xs {getCommentStyleClasses(selectedCommentStyle)}"
          style="
            background-color: {brandSettings.colors.primary}; 
            color: {brandSettings.colors.secondary};
            border-radius: {commentStyles[selectedCommentStyle]?.borderRadius || '8px'};
            opacity: {brandSettings.opacity / 100};
          ">
          {comment.message}
        </div>
      </div>
    {/each}
  </div>
  
  <!-- Empty state -->
  {#if comments.length === 0}
    <div class="text-center py-6">
      <MessageCircle size="24" class="mx-auto text-base-content/40 mb-2" />
      <p class="text-xs text-base-content/60 mb-1">No comments yet</p>
      <p class="text-xs text-base-content/40">
        {commentsPaused ? 'Resume to see live comments' : 'Comments will appear here when you go live'}
      </p>
    </div>
  {/if}
  
  <!-- Quick actions -->
  <div class="flex gap-2 pt-2 border-t border-base-200">
    <button 
      class="btn btn-xs btn-outline flex-1" 
      on:click={clearComments}
      disabled={comments.length === 0}>
      <Trash2 size="12" />
      Clear All
    </button>
    <button 
      class="btn btn-xs btn-outline flex-1" 
      on:click={pauseComments}>
      {#if commentsPaused}
        <Play size="12" />
        Resume
      {:else}
        <Pause size="12" />
        Pause
      {/if}
    </button>
  </div>
  
  <!-- Comments count -->
  {#if comments.length > 0}
    <div class="text-center">
      <div class="badge badge-neutral badge-sm">
        {comments.length} comment{comments.length !== 1 ? 's' : ''}
      </div>
    </div>
  {/if}
</div>

<style>
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.2);
    border-radius: 2px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.3);
  }
</style>