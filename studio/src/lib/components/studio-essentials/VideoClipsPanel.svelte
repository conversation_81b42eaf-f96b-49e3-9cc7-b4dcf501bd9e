<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Plus, Play, Video, Upload, MoreHorizontal } from 'lucide-svelte';
  
  let { videos } = $derived(studioEssentialsStore);
  let showUpload = $state(false);
  
  function handleVideoClick(video: any) {
    studioEssentialsStore.playVideo(video);
  }
  
  function handleVideoUpload() {
    showUpload = true;
    // TODO: Implement file upload functionality
    console.log('Opening video upload dialog...');
  }
  
  function previewVideo(video: any) {
    // TODO: Implement video preview on hover
    console.log('Previewing video:', video.name);
  }
  
  function stopPreview() {
    // TODO: Stop video preview
    console.log('Stopping preview');
  }
  
  function formatDuration(duration: string): string {
    return duration;
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Video Clips</h3>
    <button 
      class="btn btn-sm btn-ghost btn-square tooltip" 
      data-tip="Add video clip"
      on:click={handleVideoUpload}>
      <Plus size="16" />
    </button>
  </div>
  
  <!-- Video Grid -->
  <div class="space-y-3">
    {#each videos as video}
      <div class="relative group">
        <div 
          class="aspect-video bg-base-200 rounded overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all"
          on:click={() => handleVideoClick(video)}
          on:mouseenter={() => previewVideo(video)}
          on:mouseleave={() => stopPreview()}
          role="button"
          tabindex="0"
          on:keydown={(e) => e.key === 'Enter' && handleVideoClick(video)}>
          
          <img 
            src={video.thumbnail} 
            alt={video.name} 
            class="w-full h-full object-cover transition-opacity"
            class:opacity-80={video.isPlaying} />
          
          <!-- Play indicator -->
          {#if video.isPlaying}
            <div class="absolute top-2 right-2 flex items-center gap-1">
              <div class="w-2 h-2 bg-error rounded-full animate-pulse"></div>
              <span class="text-xs bg-error text-error-content px-1 rounded font-medium">LIVE</span>
            </div>
          {/if}
          
          <!-- Play button overlay on hover -->
          <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button class="btn btn-circle btn-primary btn-sm">
              <Play size="16" />
            </button>
          </div>
          
          <!-- Duration badge -->
          <div class="absolute bottom-2 right-2">
            <div class="badge badge-neutral badge-sm bg-black/70 text-white border-0">
              {formatDuration(video.duration)}
            </div>
          </div>
        </div>
        
        <div class="flex justify-between items-start mt-2">
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium truncate">{video.name}</p>
            <p class="text-xs text-base-content/60">{video.duration}</p>
          </div>
          
          <!-- More options -->
          <div class="dropdown dropdown-end">
            <button 
              class="btn btn-ghost btn-xs btn-square opacity-0 group-hover:opacity-100 transition-opacity"
              tabindex="0">
              <MoreHorizontal size="12" />
            </button>
            <ul class="dropdown-content menu bg-base-100 rounded-box w-32 p-2 shadow-lg border border-base-200">
              <li>
                <button class="text-xs" on:click={() => console.log('Edit video:', video.name)}>
                  Edit
                </button>
              </li>
              <li>
                <button class="text-xs" on:click={() => console.log('Duplicate video:', video.name)}>
                  Duplicate
                </button>
              </li>
              <li>
                <button class="text-xs text-error" on:click={() => console.log('Delete video:', video.name)}>
                  Delete
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    {/each}
    
    <!-- Empty state -->
    {#if videos.length === 0}
      <div class="text-center py-8">
        <div class="text-base-content/40 mb-2">
          <Video size="32" class="mx-auto" />
        </div>
        <p class="text-xs text-base-content/60 mb-2">No video clips yet</p>
        <p class="text-xs text-base-content/40 mb-3">Upload videos to play during your stream</p>
        <button class="btn btn-sm btn-primary" on:click={handleVideoUpload}>
          <Upload size="14" />
          Add Video
        </button>
      </div>
    {/if}
  </div>
  
  <!-- Quick Actions -->
  {#if videos.length > 0}
    <div class="flex gap-2 pt-2 border-t border-base-200">
      <button 
        class="btn btn-xs btn-outline flex-1"
        on:click={() => studioEssentialsStore.stopAllVideos()}>
        Stop All
      </button>
      <button 
        class="btn btn-xs btn-outline flex-1"
        on:click={handleVideoUpload}>
        <Plus size="12" />
        Add More
      </button>
    </div>
  {/if}
</div>

<!-- Upload Modal (placeholder) -->
{#if showUpload}
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">Upload Video Clip</h3>
      
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text">Video File</span>
        </label>
        <input type="file" class="file-input file-input-bordered w-full" accept="video/*" />
      </div>
      
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text">Video Name</span>
        </label>
        <input type="text" class="input input-bordered" placeholder="Enter video name..." />
      </div>
      
      <div class="alert alert-info text-xs mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Supported formats: MP4, MOV, AVI. Max size: 100MB</span>
      </div>
      
      <div class="modal-action">
        <button class="btn btn-primary">
          <Upload size="16" />
          Upload Video
        </button>
        <button class="btn" on:click={() => showUpload = false}>Cancel</button>
      </div>
    </div>
  </div>
{/if}