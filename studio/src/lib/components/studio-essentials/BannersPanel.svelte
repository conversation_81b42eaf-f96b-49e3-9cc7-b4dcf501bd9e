<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Plus, Edit, MoreHorizontal, ArrowRight, Type } from 'lucide-svelte';
  
  let { banners } = $derived(studioEssentialsStore);
  let showCreateBanner = $state(false);
  let editingBanner = $state<any>(null);
  let newBanner = $state({
    primaryText: '',
    secondaryText: '',
    position: 'bottom' as 'top' | 'bottom' | 'middle',
    isTicker: false
  });
  
  function createBanner() {
    if (!newBanner.primaryText.trim()) return;
    
    studioEssentialsStore.createBanner(newBanner);
    resetNewBanner();
    showCreateBanner = false;
  }
  
  function editBanner(banner: any) {
    editingBanner = banner;
    newBanner = {
      primaryText: banner.primaryText,
      secondaryText: banner.secondaryText || '',
      position: banner.position,
      isTicker: banner.isTicker
    };
    showCreateBanner = true;
  }
  
  function updateBanner() {
    if (!newBanner.primaryText.trim() || !editingBanner) return;
    
    studioEssentialsStore.updateBanner(editingBanner.id, newBanner);
    resetNewBanner();
    showCreateBanner = false;
    editingBanner = null;
  }
  
  function duplicateBanner(banner: any) {
    studioEssentialsStore.createBanner({
      primaryText: banner.primaryText + ' (Copy)',
      secondaryText: banner.secondaryText,
      position: banner.position,
      isTicker: banner.isTicker
    });
  }
  
  function deleteBanner(banner: any) {
    if (confirm(`Delete banner "${banner.primaryText}"?`)) {
      studioEssentialsStore.deleteBanner(banner.id);
    }
  }
  
  function toggleBanner(banner: any) {
    studioEssentialsStore.toggleBanner(banner);
  }
  
  function resetNewBanner() {
    newBanner = {
      primaryText: '',
      secondaryText: '',
      position: 'bottom',
      isTicker: false
    };
  }
  
  function closeModal() {
    showCreateBanner = false;
    editingBanner = null;
    resetNewBanner();
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Banners</h3>
    <button 
      class="btn btn-sm btn-primary"
      on:click={() => showCreateBanner = true}>
      <Plus size="16" />
      Create
    </button>
  </div>
  
  <!-- Banner List -->
  <div class="space-y-2">
    {#each banners as banner}
      <div class="border border-base-200 rounded-lg p-3 hover:bg-base-50 transition-colors group">
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <p class="text-sm font-semibold truncate">{banner.primaryText}</p>
            {#if banner.secondaryText}
              <p class="text-xs text-base-content/60 truncate mt-1">{banner.secondaryText}</p>
            {/if}
            
            <!-- Banner properties -->
            <div class="flex items-center gap-2 mt-2">
              {#if banner.isTicker}
                <div class="badge badge-accent badge-xs">
                  <ArrowRight size="10" class="mr-1" />
                  Ticker
                </div>
              {/if}
              <div class="badge badge-outline badge-xs capitalize">
                {banner.position}
              </div>
            </div>
          </div>
          
          <div class="flex flex-col gap-1 ml-2">
            <button 
              class="btn btn-xs"
              class:btn-primary={banner.isActive}
              class:btn-outline={!banner.isActive}
              on:click={() => toggleBanner(banner)}>
              {banner.isActive ? 'ON' : 'OFF'}
            </button>
            
            <div class="dropdown dropdown-end">
              <button class="btn btn-xs btn-ghost opacity-0 group-hover:opacity-100 transition-opacity" tabindex="0">
                <MoreHorizontal size="12" />
              </button>
              <ul class="dropdown-content menu bg-base-100 rounded-box w-32 p-2 shadow-lg border border-base-200">
                <li>
                  <button class="text-xs" on:click={() => editBanner(banner)}>
                    <Edit size="12" />
                    Edit
                  </button>
                </li>
                <li>
                  <button class="text-xs" on:click={() => duplicateBanner(banner)}>
                    Duplicate
                  </button>
                </li>
                <li>
                  <button class="text-xs text-error" on:click={() => deleteBanner(banner)}>
                    Delete
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    {/each}
    
    <!-- Empty state -->
    {#if banners.length === 0}
      <div class="text-center py-8">
        <Type size="32" class="mx-auto text-base-content/40 mb-2" />
        <p class="text-xs text-base-content/60 mb-2">No banners created yet</p>
        <p class="text-xs text-base-content/40 mb-3">Create banners to display text overlays on your stream</p>
        <button 
          class="btn btn-sm btn-primary"
          on:click={() => showCreateBanner = true}>
          Create First Banner
        </button>
      </div>
    {/if}
  </div>
  
  <!-- Active banners summary -->
  {#if studioEssentialsStore.activeBanners.length > 0}
    <div class="alert alert-success text-xs">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-4 h-4">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span>
        {studioEssentialsStore.activeBanners.length} banner{studioEssentialsStore.activeBanners.length !== 1 ? 's' : ''} active
      </span>
    </div>
  {/if}
</div>

<!-- Banner Creation/Edit Modal -->
{#if showCreateBanner}
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">
        {editingBanner ? 'Edit Banner' : 'Create Banner'}
      </h3>
      
      <div class="space-y-4">
        <!-- Primary Text -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-semibold">Primary Text</span>
            <span class="label-text-alt">{newBanner.primaryText.length}/100</span>
          </label>
          <input 
            type="text" 
            class="input input-bordered" 
            maxlength="100"
            placeholder="Enter main banner text..."
            bind:value={newBanner.primaryText} />
        </div>
        
        <!-- Secondary Text -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Secondary Text (Optional)</span>
            <span class="label-text-alt">{newBanner.secondaryText.length}/100</span>
          </label>
          <input 
            type="text" 
            class="input input-bordered" 
            maxlength="100"
            placeholder="Enter secondary text..."
            bind:value={newBanner.secondaryText} />
        </div>
        
        <!-- Banner Options -->
        <div class="grid grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Position</span>
            </label>
            <select class="select select-bordered" bind:value={newBanner.position}>
              <option value="top">Top</option>
              <option value="bottom">Bottom</option>
              <option value="middle">Middle</option>
            </select>
          </div>
          
          <div class="form-control">
            <label class="cursor-pointer label">
              <span class="label-text">Scrolling Ticker</span>
              <input 
                type="checkbox" 
                class="toggle toggle-primary" 
                bind:checked={newBanner.isTicker} />
            </label>
          </div>
        </div>
        
        <!-- Preview -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Preview</span>
          </label>
          <div class="bg-base-200 rounded p-4 min-h-16 flex items-center relative overflow-hidden">
            <div class="w-full">
              <div 
                class="text-sm font-semibold transition-all"
                class:animate-bounce={newBanner.isTicker}
                style="color: {studioEssentialsStore.brandSettings.colors.primary}">
                {newBanner.primaryText || 'Primary text will appear here'}
              </div>
              {#if newBanner.secondaryText}
                <div 
                  class="text-xs mt-1 transition-all"
                  class:animate-bounce={newBanner.isTicker}
                  style="color: {studioEssentialsStore.brandSettings.colors.secondary}">
                  {newBanner.secondaryText}
                </div>
              {/if}
            </div>
            
            <!-- Position indicator -->
            <div class="absolute right-2 bottom-2">
              <div class="badge badge-xs badge-outline capitalize">
                {newBanner.position}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-action">
        <button 
          class="btn btn-primary" 
          disabled={!newBanner.primaryText.trim()}
          on:click={editingBanner ? updateBanner : createBanner}>
          {editingBanner ? 'Update' : 'Create'} Banner
        </button>
        <button class="btn" on:click={closeModal}>
          Cancel
        </button>
      </div>
    </div>
  </div>
{/if}