<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Plus, Eye, EyeOff } from 'lucide-svelte';
  
  let { overlays } = $derived(studioEssentialsStore);
  let activeCategory = $state('all');
  
  let filteredOverlays = $derived(() => {
    return overlays.filter(overlay =>
      activeCategory === 'all' || overlay.type === activeCategory
    );
  });
  
  function toggleOverlay(overlay: any) {
    studioEssentialsStore.toggleOverlay(overlay);
  }
  
  function hideAllOverlays() {
    studioEssentialsStore.hideAllOverlays();
  }
  
  function handleAddOverlay() {
    console.log('Adding new overlay...');
    // TODO: Implement overlay upload
  }
  
  function getCategoryCount(category: string) {
    if (category === 'all') return overlays.length;
    return overlays.filter(o => o.type === category).length;
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Overlays</h3>
    <button 
      class="btn btn-sm btn-ghost btn-square tooltip" 
      data-tip="Add overlay"
      on:click={handleAddOverlay}>
      <Plus size="16" />
    </button>
  </div>
  
  <!-- Category Tabs -->
  <div class="tabs tabs-boxed tabs-xs w-full">
    <button 
      class="tab flex-1"
      class:tab-active={activeCategory === 'all'}
      on:click={() => activeCategory = 'all'}>
      All ({getCategoryCount('all')})
    </button>
    <button 
      class="tab flex-1"
      class:tab-active={activeCategory === 'transparent'}
      on:click={() => activeCategory = 'transparent'}>
      Clear
    </button>
    <button 
      class="tab flex-1"
      class:tab-active={activeCategory === 'fullscreen'}
      on:click={() => activeCategory = 'fullscreen'}>
      Full
    </button>
  </div>
  
  <!-- Overlay Grid -->
  <div class="grid grid-cols-2 gap-2">
    {#each filteredOverlays() as overlay}
      <div class="relative group">
        <button 
          class="aspect-video w-full bg-base-200 rounded overflow-hidden border-2 transition-all hover:scale-105 focus:scale-105"
          class:border-primary={overlay.isActive}
          class:border-base-300={!overlay.isActive}
          class:shadow-lg={overlay.isActive}
          class:ring-2={overlay.isActive}
          class:ring-primary={overlay.isActive}
          class:ring-opacity-50={overlay.isActive}
          on:click={() => toggleOverlay(overlay)}>
          
          <img 
            src={overlay.thumbnail} 
            alt={overlay.name} 
            class="w-full h-full object-cover transition-opacity"
            class:opacity-100={overlay.isActive}
            class:opacity-70={!overlay.isActive} />
          
          <!-- Active indicator -->
          {#if overlay.isActive}
            <div class="absolute inset-0 bg-primary/20 flex items-center justify-center">
              <div class="badge badge-primary badge-sm font-bold">ON</div>
            </div>
          {/if}
          
          <!-- Overlay type badge -->
          <div class="absolute top-1 left-1">
            <div class="badge badge-xs badge-outline bg-base-100/90 text-base-content">
              {overlay.type === 'transparent' ? 'Clear' : overlay.type === 'fullscreen' ? 'Full' : 'Layout'}
            </div>
          </div>
          
          <!-- Status icon -->
          <div class="absolute top-1 right-1">
            {#if overlay.isActive}
              <Eye size="12" class="text-primary bg-base-100/90 rounded p-1" />
            {:else}
              <EyeOff size="12" class="text-base-content/60 bg-base-100/90 rounded p-1" />
            {/if}
          </div>
        </button>
        
        <p class="text-xs mt-1 truncate text-center font-medium">{overlay.name}</p>
        <p class="text-xs text-center text-base-content/60 capitalize">{overlay.type}</p>
      </div>
    {/each}
  </div>
  
  <!-- Empty state -->
  {#if filteredOverlays().length === 0}
    <div class="text-center py-8">
      <div class="text-base-content/40 mb-2">
        <div class="w-8 h-8 mx-auto bg-base-content/10 rounded flex items-center justify-center">
          <Eye size="20" />
        </div>
      </div>
      <p class="text-xs text-base-content/60 mb-2">
        {activeCategory === 'all' ? 'No overlays yet' : `No ${activeCategory} overlays`}
      </p>
      <p class="text-xs text-base-content/40 mb-3">Add overlays to enhance your stream</p>
      <button class="btn btn-sm btn-primary" on:click={handleAddOverlay}>
        <Plus size="14" />
        Add Overlay
      </button>
    </div>
  {/if}
  
  <!-- Quick Actions -->
  {#if overlays.length > 0}
    <div class="flex gap-2 pt-2 border-t border-base-200">
      <button 
        class="btn btn-xs btn-outline flex-1"
        on:click={hideAllOverlays}>
        <EyeOff size="12" />
        Hide All
      </button>
      <button 
        class="btn btn-xs btn-outline flex-1"
        on:click={handleAddOverlay}>
        <Plus size="12" />
        Add More
      </button>
    </div>
  {/if}
  
  <!-- Active overlays summary -->
  {#if studioEssentialsStore.activeOverlays.length > 0}
    <div class="alert alert-info text-xs">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-4 h-4">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span>
        {studioEssentialsStore.activeOverlays.length} overlay{studioEssentialsStore.activeOverlays.length !== 1 ? 's' : ''} active
      </span>
    </div>
  {/if}
</div>