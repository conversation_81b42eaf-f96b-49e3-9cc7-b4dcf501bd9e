<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Palette, RotateCcw, Save, Folder } from 'lucide-svelte';
  
  let { brandSettings } = $derived(studioEssentialsStore);
  
  const brandStyles = [
    { name: 'Style 1', preview: 'Modern' },
    { name: 'Style 2', preview: 'Classic' },
    { name: 'Style 3', preview: 'Bold' },
    { name: 'Style 4', preview: 'Minimal' }
  ];
  
  const colorHarmonies = [
    { name: 'Blue Harmony', primary: '#3b82f6', secondary: '#1e40af' },
    { name: 'Green Harmony', primary: '#10b981', secondary: '#047857' },
    { name: 'Purple Harmony', primary: '#8b5cf6', secondary: '#7c3aed' },
    { name: 'Red Harmony', primary: '#ef4444', secondary: '#dc2626' }
  ];
  
  function setStyle(index: number) {
    studioEssentialsStore.updateBrandSettings({ selectedStyle: index });
  }
  
  function updateColors() {
    studioEssentialsStore.updateBrandSettings({ colors: brandSettings.colors });
  }
  
  function resetToDefaults() {
    studioEssentialsStore.resetToDefaults();
  }
  
  function applyColorHarmony(harmony: typeof colorHarmonies[0]) {
    studioEssentialsStore.updateBrandSettings({
      colors: {
        primary: harmony.primary,
        secondary: harmony.secondary
      }
    });
  }
  
  function savePreset() {
    // TODO: Implement preset saving
    console.log('Saving brand preset...');
  }
  
  function loadPreset() {
    // TODO: Implement preset loading
    console.log('Loading brand preset...');
  }
</script>

<div class="card bg-base-100 shadow-sm border-t-2 border-primary/20">
  <div class="card-header p-3 border-b border-base-200">
    <div class="flex items-center justify-between w-full">
      <h3 class="card-title text-sm font-semibold">Brand</h3>
      <div class="badge badge-primary badge-xs">Always Available</div>
    </div>
  </div>
  
  <div class="card-body p-3">
    <!-- Style Selection -->
    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text text-xs font-semibold">Style</span>
        <button class="btn btn-xs btn-ghost" on:click={resetToDefaults}>
          <RotateCcw size="12" />
          Reset
        </button>
      </label>
      <div class="grid grid-cols-2 gap-2">
        {#each brandStyles as style, index}
          <button 
            class="btn btn-sm relative"
            class:btn-primary={brandSettings.selectedStyle === index}
            class:btn-outline={brandSettings.selectedStyle !== index}
            on:click={() => setStyle(index)}>
            
            <span>Style {index + 1}</span>
            
            <!-- Style preview dots -->
            <div class="absolute bottom-1 right-1 flex gap-1">
              <div class="w-1 h-1 rounded-full bg-current opacity-60"></div>
              <div class="w-1 h-1 rounded-full bg-current opacity-40"></div>
              <div class="w-1 h-1 rounded-full bg-current opacity-20"></div>
            </div>
          </button>
        {/each}
      </div>
    </div>
    
    <!-- Color Customization -->
    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text text-xs font-semibold">Colors</span>
        <button class="btn btn-xs btn-ghost">
          <Palette size="12" />
        </button>
      </label>
      <div class="grid grid-cols-2 gap-3">
        <div class="form-control">
          <div class="relative">
            <input 
              type="color" 
              class="w-full h-10 rounded border border-base-300 cursor-pointer" 
              bind:value={brandSettings.colors.primary}
              on:change={updateColors} />
            <div class="absolute inset-0 pointer-events-none border-2 border-base-300 rounded flex items-end p-1">
              <span class="text-xs font-medium text-white bg-black/50 px-1 rounded">Primary</span>
            </div>
          </div>
        </div>
        
        <div class="form-control">
          <div class="relative">
            <input 
              type="color" 
              class="w-full h-10 rounded border border-base-300 cursor-pointer" 
              bind:value={brandSettings.colors.secondary}
              on:change={updateColors} />
            <div class="absolute inset-0 pointer-events-none border-2 border-base-300 rounded flex items-end p-1">
              <span class="text-xs font-medium text-white bg-black/50 px-1 rounded">Secondary</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Color harmony suggestions -->
      <div class="flex gap-1 mt-2">
        {#each colorHarmonies as harmony}
          <button 
            class="btn btn-xs btn-circle btn-outline tooltip"
            style="background: linear-gradient(45deg, {harmony.primary} 50%, {harmony.secondary} 50%)"
            on:click={() => applyColorHarmony(harmony)}
            data-tip={harmony.name}>
          </button>
        {/each}
      </div>
    </div>

    <!-- Frame and Effects -->
    <div class="grid grid-cols-2 gap-3 mb-4">
      <div class="form-control">
        <label class="label">
          <span class="label-text text-xs">Frame</span>
        </label>
        <select 
          class="select select-xs" 
          bind:value={brandSettings.frameStyle}
          on:change={updateColors}>
          <option value="none">None</option>
          <option value="simple">Simple</option>
          <option value="rounded">Rounded</option>
          <option value="sharp">Sharp</option>
          <option value="glow">Glow</option>
        </select>
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text text-xs">Opacity</span>
          <span class="label-text-alt text-xs">{brandSettings.opacity}%</span>
        </label>
        <input 
          type="range" 
          class="range range-xs" 
          min="50" 
          max="100" 
          bind:value={brandSettings.opacity}
          on:input={updateColors} />
      </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="flex gap-2 mb-4">
      <button class="btn btn-xs btn-outline flex-1" on:click={savePreset}>
        <Save size="12" />
        Save Preset
      </button>
      <button class="btn btn-xs btn-outline flex-1" on:click={loadPreset}>
        <Folder size="12" />
        Load Preset
      </button>
    </div>
    
    <!-- Live Preview -->
    <div class="mt-4 p-3 bg-base-200 rounded">
      <p class="text-xs font-semibold mb-2">Live Preview</p>
      <div class="space-y-2">
        <!-- Comment preview -->
        <div 
          class="inline-block px-2 py-1 rounded text-xs transition-all"
          style="
            background-color: {brandSettings.colors.primary}; 
            color: {brandSettings.colors.secondary};
            opacity: {brandSettings.opacity / 100};
            border-radius: {brandSettings.frameStyle === 'rounded' ? '8px' : brandSettings.frameStyle === 'sharp' ? '2px' : '4px'};
            {brandSettings.frameStyle === 'glow' ? `box-shadow: 0 0 8px ${brandSettings.colors.primary}40` : ''}
          ">
          Sample Comment
        </div>
        
        <!-- Banner preview -->
        <div 
          class="w-full p-2 text-center text-xs rounded transition-all"
          style="
            background-color: {brandSettings.colors.secondary}; 
            color: {brandSettings.colors.primary};
            opacity: {brandSettings.opacity / 100};
            border-radius: {brandSettings.frameStyle === 'rounded' ? '8px' : brandSettings.frameStyle === 'sharp' ? '2px' : '4px'};
            {brandSettings.frameStyle === 'glow' ? `box-shadow: 0 0 8px ${brandSettings.colors.secondary}40` : ''}
          ">
          Sample Banner Text
        </div>
      </div>
    </div>
  </div>
</div>