<script lang="ts">
  import {
    Music2,
    MessageSquare,
    Flag,
    Video,
    Eye,
    MessageCircle,
    Palette,
  } from "lucide-svelte";
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import VideoClipsPanel from './studio-essentials/VideoClipsPanel.svelte';
  import OverlaysPanel from './studio-essentials/OverlaysPanel.svelte';
  import BackgroundsPanel from './studio-essentials/BackgroundsPanel.svelte';
  import CommentsPanel from './studio-essentials/CommentsPanel.svelte';
  import BannersPanel from './studio-essentials/BannersPanel.svelte';
  import BrandControlsPanel from './studio-essentials/BrandControlsPanel.svelte';
  import { onMount } from 'svelte';

  type TabType = "videos" | "overlays" | "backgrounds" | "comments" | "banners";

  let activeTab = $state<TabType>("videos");
  
  onMount(() => {
    studioEssentialsStore.loadDefaultAssets();
  });

  function setActiveTab(tab: TabType): void {
    activeTab = tab;
  }
</script>

<div class="card w-80 bg-base-100 shadow-sm">
  <div class="card-body p-3">
    <!-- Tabs -->
    <div class="tabs tabs-boxed tabs-sm bg-base-200 gap-1 justify-evenly">
      <button
        class="tab tab-xs {activeTab === 'videos' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("videos")}
        title="Video Clips"
      >
        <Video size={16} />
      </button>
      <button
        class="tab tab-xs {activeTab === 'overlays' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("overlays")}
        title="Overlays"
      >
        <Eye size={16} />
      </button>
      <button
        class="tab tab-xs {activeTab === 'comments' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("comments")}
        title="Comments"
      >
        <MessageSquare size={16} />
      </button>
      <button
        class="tab tab-xs {activeTab === 'banners' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("banners")}
        title="Banners"
      >
        <Flag size={16} />
      </button>
    </div>

    <div class="flex flex-col justify-between h-full">
      <!-- Tab Contents -->
      <div class="flex-1 overflow-y-auto min-h-0">
        {#if activeTab === "videos"}
          <VideoClipsPanel />
        {:else if activeTab === "overlays"}
          <OverlaysPanel />
        {:else if activeTab === "comments"}
          <CommentsPanel />
        {:else if activeTab === "banners"}
          <BannersPanel />
        {/if}
      </div>

      <!-- Always-visible Brand Controls -->
      <div class="mt-2">
        <BrandControlsPanel />
      </div>
    </div>
  </div>
</div>
