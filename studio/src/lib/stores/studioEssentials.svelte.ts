import { writable, derived } from 'svelte/store';

// Types
export interface VideoClip {
  id: string;
  name: string;
  thumbnail: string;
  duration: string;
  filePath: string;
  isPlaying: boolean;
}

export interface Overlay {
  id: string;
  name: string;
  thumbnail: string;
  type: 'transparent' | 'fullscreen' | 'layout';
  filePath: string;
  isActive: boolean;
  layoutCompatibility: string[];
}

export interface Background {
  id: string;
  name: string;
  thumbnail: string;
  type: 'image' | 'video';
  filePath: string;
  isActive: boolean;
}

export interface Comment {
  id: string;
  author: string;
  message: string;
  timestamp: string;
  platform: 'youtube' | 'facebook' | 'twitch' | 'linkedin';
  avatar?: string;
}

export interface Banner {
  id: string;
  primaryText: string;
  secondaryText?: string;
  position: 'top' | 'bottom' | 'middle';
  isTicker: boolean;
  isActive: boolean;
  style: BannerStyle;
}

export interface BannerStyle {
  backgroundColor: string;
  textColor: string;
  fontSize: string;
  borderRadius: string;
}

export interface MusicTrack {
  id: string;
  name: string;
  artist: string;
  duration: string;
  filePath: string;
  currentTime?: number;
}

export interface SoundEffect {
  id: string;
  name: string;
  filePath: string;
  volume: number;
  isPlaying: boolean;
  loop: boolean;
}

export interface BrandSettings {
  selectedStyle: number;
  colors: {
    primary: string;
    secondary: string;
  };
  frameStyle: 'none' | 'simple' | 'rounded' | 'sharp' | 'glow';
  opacity: number;
}

export interface CommentStyle {
  id: number;
  name: string;
  backgroundOpacity: number;
  borderRadius: string;
  animation: boolean;
}

// State Management Class using Svelte 5 runes
class StudioEssentialsState {
  videos = $state<VideoClip[]>([]);
  overlays = $state<Overlay[]>([]);
  backgrounds = $state<Background[]>([]);
  comments = $state<Comment[]>([]);
  banners = $state<Banner[]>([]);
  musicTracks = $state<MusicTrack[]>([]);
  soundEffects = $state<SoundEffect[]>([]);
  
  // Brand and styling state
  brandSettings = $state<BrandSettings>({
    selectedStyle: 0,
    colors: { 
      primary: '#3b82f6', 
      secondary: '#1f2937' 
    },
    frameStyle: 'none',
    opacity: 100
  });
  
  commentStyles = $state<CommentStyle[]>([
    { id: 1, name: 'Style 1', backgroundOpacity: 90, borderRadius: '8px', animation: false },
    { id: 2, name: 'Style 2', backgroundOpacity: 80, borderRadius: '16px', animation: true },
    { id: 3, name: 'Style 3', backgroundOpacity: 95, borderRadius: '4px', animation: false },
    { id: 4, name: 'Style 4', backgroundOpacity: 75, borderRadius: '20px', animation: true }
  ]);
  
  selectedCommentStyle = $state(0);
  
  // Music and audio state
  musicPlaying = $state(false);
  musicVolume = $state(50);
  sfxVolume = $state(75);
  currentTrack = $state<MusicTrack | null>(null);
  loopEnabled = $state(false);
  commentsPaused = $state(false);
  
  // Derived state for active content
  activeOverlays = $derived(() => this.overlays.filter(o => o.isActive));
  activeBackground = $derived(() => this.backgrounds.find(b => b.isActive));
  activeBanners = $derived(() => this.banners.filter(b => b.isActive));
  currentCommentStyle = $derived(() => this.commentStyles[this.selectedCommentStyle]);
  
  // Actions for Video Management
  playVideo = (video: VideoClip) => {
    console.log('Playing video:', video.name);
    
    // Stop any currently playing video
    this.videos.forEach(v => v.isPlaying = false);
    
    // Play the selected video
    video.isPlaying = true;
    
    // Emit event for preview component
    this.emitPreviewUpdate('video_play', video);
    
    // Auto-stop after duration (mock implementation)
    setTimeout(() => {
      video.isPlaying = false;
      this.emitPreviewUpdate('video_stop', video);
      console.log('Video playback ended:', video.name);
    }, this.parseDuration(video.duration));
  };
  
  stopAllVideos = () => {
    this.videos.forEach(v => v.isPlaying = false);
    this.emitPreviewUpdate('all_videos_stop', null);
  };
  
  // Actions for Overlay Management
  toggleOverlay = (overlay: Overlay) => {
    overlay.isActive = !overlay.isActive;
    console.log(`Overlay ${overlay.name} ${overlay.isActive ? 'activated' : 'deactivated'}`);
    this.emitPreviewUpdate('overlay_toggle', overlay);
  };
  
  hideAllOverlays = () => {
    this.overlays.forEach(o => o.isActive = false);
    this.emitPreviewUpdate('all_overlays_hide', null);
  };
  
  // Actions for Background Management
  setBackground = (background: Background) => {
    // Deactivate all backgrounds
    this.backgrounds.forEach(b => b.isActive = false);
    
    // Activate selected background
    background.isActive = true;
    console.log('Background changed to:', background.name);
    this.emitPreviewUpdate('background_change', background);
  };
  
  // Actions for Banner Management
  createBanner = (bannerData: Partial<Banner>) => {
    const newBanner: Banner = {
      id: crypto.randomUUID(),
      primaryText: bannerData.primaryText || '',
      secondaryText: bannerData.secondaryText,
      position: bannerData.position || 'bottom',
      isTicker: bannerData.isTicker || false,
      isActive: false,
      style: {
        backgroundColor: this.brandSettings.colors.primary,
        textColor: this.brandSettings.colors.secondary,
        fontSize: '14px',
        borderRadius: this.brandSettings.frameStyle === 'rounded' ? '8px' : '4px'
      }
    };
    
    this.banners.push(newBanner);
    console.log('Banner created:', newBanner.primaryText);
    return newBanner;
  };
  
  toggleBanner = (banner: Banner) => {
    banner.isActive = !banner.isActive;
    console.log(`Banner ${banner.primaryText} ${banner.isActive ? 'activated' : 'deactivated'}`);
    this.emitPreviewUpdate('banner_toggle', banner);
  };
  
  updateBanner = (bannerId: string, updates: Partial<Banner>) => {
    const banner = this.banners.find(b => b.id === bannerId);
    if (banner) {
      Object.assign(banner, updates);
      // Update style based on current brand settings
      banner.style = {
        ...banner.style,
        backgroundColor: this.brandSettings.colors.primary,
        textColor: this.brandSettings.colors.secondary,
        borderRadius: this.brandSettings.frameStyle === 'rounded' ? '8px' : '4px'
      };
      this.emitPreviewUpdate('banner_update', banner);
    }
  };
  
  deleteBanner = (bannerId: string) => {
    const index = this.banners.findIndex(b => b.id === bannerId);
    if (index !== -1) {
      const banner = this.banners[index];
      this.banners.splice(index, 1);
      console.log('Banner deleted:', banner.primaryText);
      this.emitPreviewUpdate('banner_delete', { id: bannerId });
    }
  };
  
  // Actions for Music Management
  toggleMusic = () => {
    this.musicPlaying = !this.musicPlaying;
    console.log(`Music ${this.musicPlaying ? 'started' : 'stopped'}`);
    this.emitPreviewUpdate('music_toggle', { 
      playing: this.musicPlaying,
      track: this.currentTrack 
    });
  };
  
  playTrack = (track: MusicTrack) => {
    this.currentTrack = track;
    this.musicPlaying = true;
    console.log('Now playing:', track.name);
    this.emitPreviewUpdate('track_change', track);
  };
  
  setMusicVolume = (volume: number) => {
    this.musicVolume = Math.max(0, Math.min(100, volume));
    this.emitPreviewUpdate('music_volume', this.musicVolume);
  };
  
  // Actions for Sound Effects
  playSoundEffect = (sfx: SoundEffect) => {
    if (sfx.isPlaying) return;
    
    sfx.isPlaying = true;
    console.log('Playing sound effect:', sfx.name);
    this.emitPreviewUpdate('sfx_play', sfx);
    
    // Mock playback duration
    setTimeout(() => {
      sfx.isPlaying = false;
      console.log('Sound effect ended:', sfx.name);
    }, 2000);
  };
  
  setSFXVolume = (volume: number) => {
    this.sfxVolume = Math.max(0, Math.min(100, volume));
    this.emitPreviewUpdate('sfx_volume', this.sfxVolume);
  };
  
  // Actions for Brand Management
  updateBrandSettings = (settings: Partial<BrandSettings>) => {
    Object.assign(this.brandSettings, settings);
    console.log('Brand settings updated');
    
    // Update all banner styles to match new brand settings
    this.banners.forEach(banner => {
      banner.style = {
        ...banner.style,
        backgroundColor: this.brandSettings.colors.primary,
        textColor: this.brandSettings.colors.secondary,
        borderRadius: this.brandSettings.frameStyle === 'rounded' ? '8px' : '4px'
      };
    });
    
    this.emitPreviewUpdate('brand_update', this.brandSettings);
  };
  
  setCommentStyle = (styleIndex: number) => {
    this.selectedCommentStyle = Math.max(0, Math.min(this.commentStyles.length - 1, styleIndex));
    console.log('Comment style changed to:', this.commentStyles[this.selectedCommentStyle].name);
    this.emitPreviewUpdate('comment_style_change', this.currentCommentStyle);
  };
  
  // Actions for Comments
  addComment = (comment: Omit<Comment, 'id'>) => {
    if (this.commentsPaused) return;
    
    const newComment: Comment = {
      id: crypto.randomUUID(),
      ...comment,
      timestamp: new Date().toLocaleTimeString()
    };
    
    this.comments.unshift(newComment);
    
    // Keep only last 50 comments for performance
    if (this.comments.length > 50) {
      this.comments = this.comments.slice(0, 50);
    }
    
    this.emitPreviewUpdate('comment_add', newComment);
  };
  
  clearComments = () => {
    this.comments = [];
    console.log('Comments cleared');
    this.emitPreviewUpdate('comments_clear', null);
  };
  
  toggleCommentsPause = () => {
    this.commentsPaused = !this.commentsPaused;
    console.log(`Comments ${this.commentsPaused ? 'paused' : 'resumed'}`);
  };
  
  // Helper methods
  private parseDuration = (duration: string): number => {
    // Parse duration like "0:30" to milliseconds
    const parts = duration.split(':');
    if (parts.length === 2) {
      const [minutes, seconds] = parts.map(Number);
      return (minutes * 60 + seconds) * 1000;
    }
    return 5000; // Default 5 seconds
  };
  
  private emitPreviewUpdate = (type: string, data: any) => {
    // Dispatch custom event for preview component
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('studio-update', {
        detail: { type, data, timestamp: Date.now() }
      }));
    }
  };
  
  // Initialization and data loading
  loadDefaultAssets = async () => {
    console.log('Loading default studio assets...');
    
    // Load default overlays
    this.overlays = [
      {
        id: '1',
        name: 'Lower Third',
        thumbnail: '/assets/overlays/lower-third-thumb.png',
        type: 'transparent',
        filePath: '/assets/overlays/lower-third.png',
        isActive: false,
        layoutCompatibility: ['single', 'dual']
      },
      {
        id: '2',
        name: 'Full Screen Logo',
        thumbnail: '/assets/overlays/fullscreen-logo-thumb.png',
        type: 'fullscreen',
        filePath: '/assets/overlays/fullscreen-logo.png',
        isActive: false,
        layoutCompatibility: ['all']
      },
      {
        id: '3',
        name: 'Corner Logo',
        thumbnail: '/assets/overlays/corner-logo-thumb.png',
        type: 'transparent',
        filePath: '/assets/overlays/corner-logo.png',
        isActive: false,
        layoutCompatibility: ['single', 'dual', 'multi']
      }
    ];
    
    // Load default backgrounds
    this.backgrounds = [
      {
        id: '1',
        name: 'Studio Background',
        thumbnail: '/assets/backgrounds/studio-thumb.jpg',
        type: 'image',
        filePath: '/assets/backgrounds/studio.jpg',
        isActive: true
      },
      {
        id: '2',
        name: 'Abstract Blue',
        thumbnail: '/assets/backgrounds/abstract-blue-thumb.jpg',
        type: 'image',
        filePath: '/assets/backgrounds/abstract-blue.jpg',
        isActive: false
      },
      {
        id: '3',
        name: 'Animated Background',
        thumbnail: '/assets/backgrounds/animated-thumb.jpg',
        type: 'video',
        filePath: '/assets/backgrounds/animated.mp4',
        isActive: false
      }
    ];
    
    // Load default sound effects
    this.soundEffects = [
      {
        id: '1',
        name: 'Air Horn',
        filePath: '/assets/sfx/airhorn.mp3',
        volume: 80,
        isPlaying: false,
        loop: false
      },
      {
        id: '2',
        name: 'Applause',
        filePath: '/assets/sfx/applause.mp3',
        volume: 70,
        isPlaying: false,
        loop: false
      },
      {
        id: '3',
        name: 'Notification',
        filePath: '/assets/sfx/notification.mp3',
        volume: 60,
        isPlaying: false,
        loop: false
      },
      {
        id: '4',
        name: 'Transition',
        filePath: '/assets/sfx/transition.mp3',
        volume: 50,
        isPlaying: false,
        loop: false
      }
    ];
    
    // Load sample video clips
    this.videos = [
      {
        id: '1',
        name: 'Intro Video',
        thumbnail: '/assets/videos/intro-thumb.jpg',
        duration: '0:15',
        filePath: '/assets/videos/intro.mp4',
        isPlaying: false
      },
      {
        id: '2',
        name: 'Transition Clip',
        thumbnail: '/assets/videos/transition-thumb.jpg',
        duration: '0:05',
        filePath: '/assets/videos/transition.mp4',
        isPlaying: false
      }
    ];
    
    // Load sample music tracks
    this.musicTracks = [
      {
        id: '1',
        name: 'Ambient Background',
        artist: 'Studio Music',
        duration: '3:24',
        filePath: '/assets/music/ambient.mp3'
      },
      {
        id: '2',
        name: 'Upbeat Theme',
        artist: 'Studio Music',
        duration: '2:45',
        filePath: '/assets/music/upbeat.mp3'
      }
    ];
    
    console.log('Default assets loaded successfully');
  };
  
  // Asset management methods
  addVideoClip = (videoData: Omit<VideoClip, 'id' | 'isPlaying'>) => {
    const newVideo: VideoClip = {
      id: crypto.randomUUID(),
      ...videoData,
      isPlaying: false
    };
    this.videos.push(newVideo);
    console.log('Video clip added:', newVideo.name);
    return newVideo;
  };
  
  addOverlay = (overlayData: Omit<Overlay, 'id' | 'isActive'>) => {
    const newOverlay: Overlay = {
      id: crypto.randomUUID(),
      ...overlayData,
      isActive: false
    };
    this.overlays.push(newOverlay);
    console.log('Overlay added:', newOverlay.name);
    return newOverlay;
  };
  
  addBackground = (backgroundData: Omit<Background, 'id' | 'isActive'>) => {
    const newBackground: Background = {
      id: crypto.randomUUID(),
      ...backgroundData,
      isActive: false
    };
    this.backgrounds.push(newBackground);
    console.log('Background added:', newBackground.name);
    return newBackground;
  };
  
  addSoundEffect = (sfxData: Omit<SoundEffect, 'id' | 'isPlaying'>) => {
    const newSFX: SoundEffect = {
      id: crypto.randomUUID(),
      ...sfxData,
      isPlaying: false
    };
    this.soundEffects.push(newSFX);
    console.log('Sound effect added:', newSFX.name);
    return newSFX;
  };
  
  // Reset methods
  resetToDefaults = () => {
    this.brandSettings = {
      selectedStyle: 0,
      colors: { primary: '#3b82f6', secondary: '#1f2937' },
      frameStyle: 'none',
      opacity: 100
    };
    this.selectedCommentStyle = 0;
    this.musicVolume = 50;
    this.sfxVolume = 75;
    console.log('Settings reset to defaults');
    this.emitPreviewUpdate('settings_reset', null);
  };
}

// Create and export the singleton store instance
export const studioEssentialsStore = new StudioEssentialsState();

// Add some mock comments for demonstration
if (typeof window !== 'undefined') {
  // Simulate live comments
  setInterval(() => {
    if (!studioEssentialsStore.commentsPaused) {
      const mockComments = [
        { author: 'StreamFan123', message: 'Great stream! 🔥', platform: 'youtube' as const, timestamp: '' },
        { author: 'TechViewer', message: 'Love the setup!', platform: 'twitch' as const, timestamp: '' },
        { author: 'LiveWatcher', message: 'Amazing content as always', platform: 'facebook' as const, timestamp: '' },
        { author: 'ContentLover', message: 'Keep it up! 👏', platform: 'linkedin' as const, timestamp: '' }
      ];
      
      const randomComment = mockComments[Math.floor(Math.random() * mockComments.length)];
      studioEssentialsStore.addComment(randomComment);
    }
  }, 8000); // Add a comment every 8 seconds
}