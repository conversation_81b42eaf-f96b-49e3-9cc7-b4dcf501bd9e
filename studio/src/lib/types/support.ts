// Browser support detection types based on @kixelated/hang support feature
export type SupportRole = "core" | "watch" | "publish" | "all";
export type SupportLevel = "full" | "partial" | "none";

// Audio codec support
export interface AudioCodecSupport {
	aac: boolean;
	opus: boolean;
}

// Video codec support with hardware/software detection
export interface VideoCodecInfo {
	hardware?: boolean; // undefined when we can't detect hardware acceleration
	software: boolean;
}

// Video codec support
export interface VideoCodecSupport {
	h264: VideoCodecInfo;
	h265: VideoCodecInfo;
	vp8: VideoCodecInfo;
	vp9: VideoCodecInfo;
	av1: VideoCodecInfo;
}

// Full browser support information
export interface BrowserSupport {
	webtransport: boolean;
	audio: {
		capture: boolean;
		encoding: AudioCodecSupport | undefined;
		decoding: AudioCodecSupport | undefined;
		render: boolean;
	};
	video: {
		capture: SupportLevel;
		encoding: VideoCodecSupport | undefined;
		decoding: VideoCodecSupport | undefined;
		render: boolean;
	};
}

// Support check configuration
export interface SupportCheckConfig {
	role?: SupportRole;
	show?: SupportLevel;
	onResult?: (support: BrowserSupport) => void;
	onError?: (error: Error) => void;
}

// Media constraints for testing
export interface MediaTestConstraints {
	audio?: boolean | MediaTrackConstraints;
	video?: boolean | MediaTrackConstraints;
}

// Support check result
export interface SupportCheckResult {
	overall: SupportLevel;
	core: SupportLevel;
	watch: SupportLevel;
	publish: SupportLevel;
	details: BrowserSupport;
	recommendations: string[];
	warnings: string[];
}