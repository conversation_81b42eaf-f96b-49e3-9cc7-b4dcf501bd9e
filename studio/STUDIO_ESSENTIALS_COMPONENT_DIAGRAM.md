# Studio Essentials Component Architecture
*Visual guide and implementation specifications for Evmux clone components*

## Component Hierarchy & Integration

```mermaid
graph TB
    A["+page.svelte<br/>Main Studio Layout"] --> B["Header.svelte"]
    A --> C["Sidebar.svelte<br/>(Dynamic Scene & Pro Scenes)"]
    A --> D["Controls.svelte<br/>⭐ ENHANCED FOR STUDIO ESSENTIALS"]
    A --> E["Preview.svelte<br/>⭐ ENHANCED FOR REAL-TIME UPDATES"]
    A --> F["Footer.svelte"]
    A --> G["StatusBar.svelte"]
    
    D --> H["Studio Essentials Tabs"]
    H --> I["VideoClipsPanel.svelte"]
    H --> J["OverlaysPanel.svelte"]
    H --> K["BackgroundsPanel.svelte"]
    H --> L["CommentsPanel.svelte"]
    H --> M["BannersPanel.svelte"]
    H --> N["MusicPanel.svelte"]
    
    D --> O["BrandControlsPanel.svelte<br/>⭐ ALWAYS VISIBLE"]
    
    I --> P["AssetUploadModal.svelte"]
    J --> P
    K --> P
    M --> Q["BannerCreateModal.svelte"]
    N --> R["MusicPlaylistModal.svelte"]
    
    S["studioEssentials.svelte.ts<br/>⭐ CENTRAL STATE STORE"] --> D
    S --> E
    S --> T["Preview Update Events"]
    
    E --> U["Real-time Overlay Rendering"]
    E --> V["Background Display"]
    E --> W["Banner Display"]
    E --> X["Video Playback"]
    
    style D fill:#e1f5fe
    style E fill:#e8f5e8
    style S fill:#fff3e0
    style O fill:#fce4ec
```

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant User
    participant Panel as Studio Panel
    participant Store as studioEssentials Store
    participant Preview as Preview Component
    participant Event as Custom Events
    
    User->>Panel: Clicks overlay toggle
    Panel->>Store: toggleOverlay(overlay)
    Store->>Store: Update overlay.isActive
    Store->>Event: Emit 'studio-update' event
    Event->>Preview: Listen for event
    Preview->>Preview: Update overlay display
    Store->>Panel: Reactive update
    Panel->>Panel: Update UI state
```

## DaisyUI Component Mapping

### Primary Components Used
| Studio Essential | Primary DaisyUI Components | Key Classes |
|-----------------|---------------------------|-------------|
| **Video Clips** | `card`, `btn`, `badge` | `aspect-video`, `btn-ghost`, `badge-primary` |
| **Overlays** | `card`, `tabs`, `btn` | `grid-cols-2`, `border-primary`, `tab-active` |
| **Backgrounds** | `card`, `btn` | `aspect-video`, `border-primary`, `shadow-md` |
| **Comments** | `chat`, `dropdown`, `form-control` | `chat-bubble`, `dropdown-content`, `select-xs` |
| **Banners** | `card`, `modal`, `form-control` | `badge-accent`, `modal-open`, `input-bordered` |
| **Music/SFX** | `card`, `range`, `progress` | `range-primary`, `progress-primary`, `loading-spinner` |
| **Brand Controls** | `card`, `form-control` | `border-t-2`, `grid-cols-2`, `badge-xs` |

### Color Scheme Integration
```css
/* DaisyUI semantic colors used throughout */
--primary: /* User's brand primary color */
--secondary: /* User's brand secondary color */
--base-100: /* Main background */
--base-200: /* Card backgrounds */
--base-300: /* Borders and dividers */
--base-content: /* Text color */
--accent: /* Special highlights */
--error: /* Live indicators */
```

## Component Specifications

### 1. Enhanced Controls.svelte Structure

```svelte
<!-- File: studio/src/lib/components/Controls.svelte -->
<div class="w-80 flex flex-col bg-base-100 shadow-sm rounded-lg overflow-hidden">
  <!-- TAB NAVIGATION -->
  <div class="tabs tabs-bordered bg-base-200 px-2 py-1">
    <!-- 6 Main Tabs: Videos, Overlays, Backgrounds, Comments, Banners, Music -->
  </div>
  
  <!-- SCROLLABLE TAB CONTENT -->
  <div class="flex-1 overflow-y-auto min-h-0">
    <!-- Dynamic content based on active tab -->
  </div>
  
  <!-- BRAND CONTROLS (ALWAYS VISIBLE) -->
  <div class="border-t-2 border-primary/20 bg-base-100">
    <!-- Brand settings that affect all other components -->
  </div>
</div>
```

### 2. Enhanced Preview.svelte Structure

```svelte
<!-- File: studio/src/lib/components/Preview.svelte -->
<div class="preview-container relative bg-base-300 rounded-lg overflow-hidden">
  <!-- BACKGROUND LAYER (z-index: -10) -->
  <div class="absolute inset-0 -z-10">
    <!-- Active background image/video -->
  </div>
  
  <!-- MAIN CONTENT (z-index: 0) -->
  <div class="relative">
    <!-- Existing camera feeds and dynamic scene content -->
  </div>
  
  <!-- OVERLAY LAYER (z-index: 5) -->
  <div class="absolute inset-0 pointer-events-none">
    <!-- Active overlays (transparent PNG graphics) -->
  </div>
  
  <!-- BANNER LAYER (z-index: 10) -->
  <div class="absolute inset-x-0">
    <!-- Active banners (top, middle, bottom positioned) -->
  </div>
  
  <!-- VIDEO PLAYBACK LAYER (z-index: 15) -->
  <div class="absolute inset-0">
    <!-- Full-screen video playback when active -->
  </div>
  
  <!-- COMMENTS OVERLAY (z-index: 20) -->
  <div class="absolute inset-0 pointer-events-none">
    <!-- Live comments with brand styling -->
  </div>
</div>
```

## Implementation Priority Matrix

### Phase 1: Foundation (Essential)
```
High Impact, Low Complexity:
✅ Brand Controls Panel
✅ Video Clips Panel (basic)
✅ Overlays Panel (basic)
✅ State Management Setup
```

### Phase 2: Core Functionality (Important)
```
High Impact, Medium Complexity:
🔄 Background Management
🔄 Banner Creation & Display
🔄 Comments Styling System
🔄 Preview Integration
```

### Phase 3: Advanced Features (Nice to Have)
```
Medium Impact, High Complexity:
⏳ Music & SFX Management
⏳ Asset Upload System
⏳ Real-time Comment Feed
⏳ Advanced Transitions
```

## Component Integration Patterns

### 1. State Synchronization Pattern
```typescript
// Each panel component follows this pattern:
export class StudioPanel {
  // Reactive state from store
  let { items, activeItems } = $derived(studioEssentialsStore);
  
  // Local UI state
  let selectedItem = $state(null);
  let showModal = $state(false);
  
  // Actions that update store
  function toggleItem(item) {
    studioEssentialsStore.toggleItem(item);
  }
  
  // Store automatically emits events for preview updates
}
```

### 2. Event Communication Pattern
```typescript
// Store emits events for preview updates
studioEssentialsStore.emitPreviewUpdate('overlay_toggle', overlay);

// Preview component listens and responds
window.addEventListener('studio-update', (event) => {
  const { type, data } = event.detail;
  handlePreviewUpdate(type, data);
});
```

### 3. Brand Consistency Pattern
```svelte
<!-- All brand-affected components use reactive styling -->
<div 
  class="comment-bubble"
  style="
    background-color: {brandSettings.colors.primary}; 
    color: {brandSettings.colors.secondary};
    border-radius: {brandSettings.frameStyle === 'rounded' ? '8px' : '4px'};
  ">
  {comment.message}
</div>
```

## File Organization Strategy

```
studio/src/lib/
├── components/
│   ├── Controls.svelte ⭐ ENHANCED
│   ├── Preview.svelte ⭐ ENHANCED
│   └── studio-essentials/
│       ├── VideoClipsPanel.svelte
│       ├── OverlaysPanel.svelte
│       ├── BackgroundsPanel.svelte
│       ├── CommentsPanel.svelte
│       ├── BannersPanel.svelte
│       ├── MusicPanel.svelte
│       └── BrandControlsPanel.svelte
├── stores/
│   └── studioEssentials.svelte.ts ⭐ NEW
├── types/
│   └── studioEssentials.ts ⭐ NEW
└── utils/
    ├── assetProcessor.ts ⭐ NEW
    └── previewRenderer.ts ⭐ NEW
```

## Testing Strategy

### Component Testing
```typescript
// Test each panel component individually
describe('VideoClipsPanel', () => {
  test('plays video on click', () => {
    // Mock store, render component, simulate click, verify state
  });
  
  test('shows upload modal', () => {
    // Test modal opening and closing
  });
});
```

### Integration Testing
```typescript
// Test store integration
describe('StudioEssentials Integration', () => {
  test('overlay toggle updates preview', () => {
    // Test event flow from panel to preview
  });
  
  test('brand changes affect all components', () => {
    // Test brand consistency across components
  });
});
```

### Visual Testing
```typescript
// Test DaisyUI styling consistency
describe('Visual Consistency', () => {
  test('components follow brand colors', () => {
    // Test color application
  });
  
  test('responsive layout works', () => {
    // Test different screen sizes
  });
});
```

## Performance Optimizations

### 1. Lazy Loading
```svelte
<!-- Only load heavy components when needed -->
{#await import('./studio-essentials/VideoClipsPanel.svelte')}
  <div class="loading loading-spinner"></div>
{:then VideoClipsPanel}
  <svelte:component this={VideoClipsPanel.default} />
{/await}
```

### 2. Virtual Scrolling
```svelte
<!-- For large asset lists -->
<VirtualList items={videos} itemHeight={80}>
  <svelte:fragment slot="item" let:item>
    <VideoClipItem video={item} />
  </svelte:fragment>
</VirtualList>
```

### 3. Debounced Updates
```typescript
// Debounce brand color changes
const debouncedBrandUpdate = debounce((settings) => {
  studioEssentialsStore.updateBrandSettings(settings);
}, 300);
```

## Accessibility Features

### 1. Keyboard Navigation
```svelte
<!-- All interactive elements support keyboard -->
<button 
  class="btn"
  on:click={toggleOverlay}
  on:keydown={(e) => e.key === 'Enter' && toggleOverlay()}>
  Toggle Overlay
</button>
```

### 2. Screen Reader Support
```svelte
<!-- Proper ARIA labels and live regions -->
<div 
  role="tablist" 
  aria-label="Studio Controls">
  <button 
    role="tab" 
    aria-selected={activeTab === 'videos'}
    aria-controls="video-panel">
    Videos
  </button>
</div>

<div 
  id="video-panel" 
  role="tabpanel" 
  aria-live="polite">
  <!-- Panel content -->
</div>
```

### 3. High Contrast Support
```css
/* Support for high contrast mode */
@media (prefers-contrast: high) {
  .studio-panel {
    border: 2px solid;
  }
}
```

This component architecture provides a clear roadmap for implementing the Studio Essentials while maintaining clean separation of concerns, excellent user experience, and professional-grade functionality matching the Evmux platform.