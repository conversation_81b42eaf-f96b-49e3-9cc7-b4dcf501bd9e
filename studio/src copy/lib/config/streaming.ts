import type { StreamQuality, StreamDevice } from '$lib/types/streaming';

// Streaming configuration for Edify Studio
export const STREAMING_CONFIG = {
	// MoQ Relay server configuration
	relay: {
		// Default to localhost for development
		// In production, this should be configurable via environment variables
		url: import.meta.env.VITE_MOQ_RELAY_URL || 'http://localhost:4443',
		
		// Connection settings
		connection: {
			reload: true,
			delay: 1000,
			maxDelay: 30000
		}
	},

	// Default stream paths
	paths: {
		host: 'studio/host',
		guestPrefix: 'studio/guest'
	},

	// Quality settings
	quality: {
		default: 'HD' as StreamQuality,
		options: ['4K', 'HD', 'SD'] as const
	},

	// Device settings
	device: {
		default: 'camera' as StreamDevice,
		options: ['camera', 'screen'] as const
	},

	// Studio branding
	branding: {
		name: 'Edify Studio',
		logo: 'E',
		watermarkText: 'Edify Studio'
	}
};