<script lang="ts">
  import { Plus, Info } from "lucide-svelte";

  let showAddScene = $state<boolean>(false);
  let newSceneName = $state<string>("");
  let scenes = $state<string[]>([
    "My Scene",
    "Interview Setup",
    "Presentation Mode",
  ]);
  let activeScene = $state<string>("My Scene");

  function addScene(): void {
    if (newSceneName.trim()) {
      console.log("Adding scene:", newSceneName);
      scenes.push(newSceneName.trim());
      activeScene = newSceneName.trim();
      newSceneName = "";
      showAddScene = false;
    }
  }

  function toggleAddScene(): void {
    showAddScene = !showAddScene;
    if (!showAddScene) {
      newSceneName = "";
    }
  }

  function selectScene(scene: string): void {
    activeScene = scene;
  }
</script>

<div class="w-[160px] flex flex-col gap-4">
  <!-- Dynamic Scene -->
  <div class="card card-compact bg-base-100 shadow-sm">
    <div class="card-body">
      <div class="card-title text-sm gap-1 items-center">
        <span>Dynamic Scene</span>
        <div
          class="tooltip tooltip-right"
          data-tip="Auto-arranged scene layout"
        >
          <Info size={14} class="text-base-content/40" />
        </div>
      </div>
      <div class="bg-base-200 rounded p-2 flex items-center justify-center h-20">
        <div class="bg-base-300 w-10 h-6 flex gap-1">
          <div class="w-4 h-6 bg-base-content/20"></div>
          <div class="w-4 h-6 bg-base-content/20"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pro Scenes -->
  <div class="card card-compact bg-base-100 shadow-sm flex-1">
    <div class="card-body">
      <div class="card-title text-sm gap-1 items-center">
        <span>Pro Scenes</span>
        <div class="tooltip tooltip-right" data-tip="Custom scene layouts">
          <Info size={14} class="text-base-content/40" />
        </div>
      </div>

      <!-- Scene List -->
      <div class="flex flex-col gap-2 mb-4">
        {#each scenes as scene}
          <button
            class="btn btn-sm w-full justify-start {scene === activeScene
              ? 'btn-primary'
              : 'btn-ghost'}"
            onclick={() => selectScene(scene)}
          >
            {scene}
          </button>
        {/each}
      </div>

      <!-- Add Scene Button/Form -->
      {#if showAddScene}
        <div class="flex flex-col gap-2">
          <input
            type="text"
            placeholder="Scene name"
            class="input input-bordered input-xs w-full"
            bind:value={newSceneName}
            onkeydown={(e) => e.key === "Enter" && addScene()}
          />
          <div class="join w-full">
            <button class="btn btn-primary btn-xs join-item flex-1" onclick={addScene}>
              Add
            </button>
            <button class="btn btn-ghost btn-xs join-item" onclick={toggleAddScene}>
              ×
            </button>
          </div>
        </div>
      {:else}
        <button class="btn btn-outline btn-xs w-full gap-1" onclick={toggleAddScene}>
          <Plus size={14} />
          <span class="text-xs">Add Scene</span>
        </button>
      {/if}
    </div>
  </div>
</div>
