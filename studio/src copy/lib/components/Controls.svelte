<script lang="ts">
  import {
    Music2,
    MessageSquare,
    Flag,
    Layers,
    MessageCircle,
    Lock,
  } from "lucide-svelte";

  type TabType = "layers" | "music" | "comments" | "banners";

  interface Layer {
    id: number;
    name: string;
    type: "image" | "user";
    locked: boolean;
    visible: boolean;
    avatar?: string;
    company?: string;
  }

  let activeTab = $state<TabType>("layers");
  let layers = $state<Layer[]>([
    { id: 1, name: "Logo", type: "image", locked: false, visible: true },
    {
      id: 2,
      name: "<PERSON>",
      type: "user",
      locked: false,
      visible: true,
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      company: "evmux",
    },
    {
      id: 3,
      name: "Elad",
      type: "user",
      locked: false,
      visible: true,
      avatar: "https://randomuser.me/api/portraits/men/33.jpg",
      company: "evmux",
    },
    { id: 4, name: "Background", type: "image", locked: true, visible: true },
  ]);

  function setActiveTab(tab: TabType): void {
    activeTab = tab;
  }

  function addNewLayer(): void {
    const newLayer: Layer = {
      id: layers.length + 1,
      name: `Layer ${layers.length + 1}`,
      type: "image",
      locked: false,
      visible: true,
    };
    layers.push(newLayer);
  }

  function toggleLayerLock(layerId: number): void {
    const layer = layers.find((l) => l.id === layerId);
    if (layer) {
      layer.locked = !layer.locked;
    }
  }

  function toggleLayerVisibility(layerId: number): void {
    const layer = layers.find((l) => l.id === layerId);
    if (layer) {
      layer.visible = !layer.visible;
    }
  }
</script>

<div class="card w-[300px] bg-base-100 shadow-sm">
  <div class="card-body p-3">
    <!-- Tabs -->
    <div class="tabs tabs-boxed tabs-sm bg-base-200 gap-1 justify-evenly">
      <button
        class="tab tab-xs {activeTab === 'layers' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("layers")}
      >
        <Layers size={20} />
      </button>
      <button
        class="tab tab-xs {activeTab === 'music' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("music")}
      >
        <Music2 size={20} />
      </button>
      <button
        class="tab tab-xs {activeTab === 'comments' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("comments")}
      >
        <MessageSquare size={20} />
      </button>
      <button
        class="tab tab-xs {activeTab === 'banners' ? 'tab-active' : ''}"
        onclick={() => setActiveTab("banners")}
      >
        <Flag size={20} />
      </button>
    </div>

    <div class="flex flex-col justify-between h-full">
      <!-- Tab Contents -->
      <div class="p-5">
        {#if activeTab === "layers"}
          <!-- Add New Layer Button -->
          <button
            class="btn btn-primary w-full mb-6"
            onclick={addNewLayer}
          >
            Add New Layer
          </button>

          <!-- Layer List -->
          <div class="flex flex-col gap-3 overflow-y-auto">
            {#each layers as layer (layer.id)}
              <div
                class="flex items-center gap-2 p-2 rounded-lg group {layer.visible
                  ? 'bg-primary/10 border border-primary/20'
                  : 'bg-base-200'}"
              >
                <input
                  type="checkbox"
                  class="checkbox checkbox-xs"
                  checked={layer.visible}
                  onchange={() => toggleLayerVisibility(layer.id)}
                />

                {#if layer.type === "user" && layer.avatar}
                  <div class="avatar">
                    <div class="w-6 h-6 rounded-full">
                      <img src={layer.avatar} alt={layer.name} />
                    </div>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium">{layer.name}</p>
                    <p class="text-xs text-base-content/60">{layer.company}</p>
                  </div>
                {:else}
                  <div class="p-1 bg-base-100 rounded border border-base-300">
                    <Layers size={16} />
                  </div>
                  <span class="text-sm flex-1">{layer.name}</span>
                {/if}

                {#if layer.locked}
                  <button
                    class="btn btn-ghost btn-xs"
                    onclick={() => toggleLayerLock(layer.id)}
                    title="Unlock layer"
                  >
                    <Lock size={14} class="text-warning" />
                  </button>
                {:else}
                  <button
                    class="btn btn-ghost btn-xs opacity-0 group-hover:opacity-100"
                    onclick={() => toggleLayerLock(layer.id)}
                    title="Lock layer"
                  >
                    <Lock size={14} class="text-base-content/40" />
                  </button>
                {/if}
              </div>
            {/each}
          </div>
        {:else if activeTab === "music"}
          <div class="hero h-32">
            <div class="hero-content text-center">
              <div>
                <Music2 size={32} class="mx-auto mb-2 text-base-content/60" />
                <p class="text-sm text-base-content/60">Music controls coming soon</p>
              </div>
            </div>
          </div>
        {:else if activeTab === "comments"}
          <div class="hero h-32">
            <div class="hero-content text-center">
              <div>
                <MessageSquare size={32} class="mx-auto mb-2 text-base-content/60" />
                <p class="text-sm text-base-content/60">Comments panel coming soon</p>
              </div>
            </div>
          </div>
        {:else if activeTab === "banners"}
          <div class="hero h-32">
            <div class="hero-content text-center">
              <div>
                <Flag size={32} class="mx-auto mb-2 text-base-content/60" />
                <p class="text-sm text-base-content/60">Banner controls coming soon</p>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- Bottom Controls -->
      <div class="divider my-2"></div>
      <div class="join join-horizontal w-full">
        <button class="btn btn-ghost btn-xs join-item flex-1">
          <div class="w-3 h-3 bg-primary rounded"></div>
          Brand
        </button>
        <button class="btn btn-ghost btn-xs join-item flex-1">
          <MessageCircle size={12} />
          Private Chat
        </button>
      </div>
    </div>
  </div>
</div>
