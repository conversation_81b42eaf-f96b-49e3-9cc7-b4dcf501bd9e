<script lang="ts">
  import { Mic, Video, Settings, Layout, Calendar } from "lucide-svelte";
  import { streamingStore } from "$lib/stores/streaming.svelte";
  import { onMount } from "svelte";

  let isMuted = $state<boolean>(false);
  let isVideoOff = $state<boolean>(false);
  let showSettings = $state<boolean>(false);
  let showLayoutSelector = $state<boolean>(false);
  let selectedLayout = $state<string>("single");
  let videoElement: HTMLVideoElement | undefined;
  let previewContainer: HTMLDivElement;

  // Layout configurations
  const layouts = [
    {
      id: "single",
      name: "Single Participant",
      description: "One large participant view",
      useCase: "Solo presenter, keynote",
    },
    {
      id: "horizontal",
      name: "Two-Panel Horizontal",
      description: "Equal halves horizontally",
      useCase: "Two speakers with equal focus",
    },
    {
      id: "vertical",
      name: "Two-Panel Vertical",
      description: "Equal halves vertically",
      useCase: "Side-by-side comparison, dual hosts",
    },
    {
      id: "pip",
      name: "Two-Panel PiP",
      description: "One large + one small floating",
      useCase: "Host + guest, spotlight + support",
    },
    {
      id: "three",
      name: "Three-Panel",
      description: "1 portrait + 2 vertical squares",
      useCase: "Small panel discussion",
    },
    {
      id: "four-vertical",
      name: "Four-Panel (Vertical)",
      description: "1 vertical rectangle + 3 thumbnails",
      useCase: "One main speaker + 3 participants",
    },
    {
      id: "four-horizontal",
      name: "Four-Panel (Horizontal)",
      description: "1 horizontal rectangle + 3 thumbnails",
      useCase: "Wide shot with multiple supports",
    },
  ];

  // Get reactive state from streaming store
  let isStreaming = $derived(streamingStore.isStreaming);
  let connectionStatus = $derived(streamingStore.connectionStatus);

  function toggleMute(): void {
    isMuted = !isMuted;
    streamingStore.setAudioEnabled(!isMuted);
  }

  function toggleVideo(): void {
    isVideoOff = !isVideoOff;
    streamingStore.setVideoEnabled(!isVideoOff);
  }

  function toggleSettings(): void {
    showSettings = !showSettings;
  }

  function selectLayout(layoutId: string): void {
    selectedLayout = layoutId;
    console.log("Selected layout:", layoutId);
    // TODO: Implement actual layout switching logic
  }

  function openCalendar(): void {
    console.log("Opening calendar");
  }

  function handleQualityToggle(event: Event): void {
    const target = event.target as HTMLInputElement;
    streamingStore.setStreamQuality(target.checked ? "4K" : "HD");
  }

  // Setup video preview when publisher is available
  onMount(() => {
    const updateVideoPreview = () => {
      // Remove existing video element
      if (videoElement) {
        videoElement.remove();
        videoElement = undefined;
      }

      // Get new video element from publisher
      const publisherVideo = streamingStore.getPublisherVideoElement();
      if (publisherVideo && previewContainer) {
        videoElement = publisherVideo;
        videoElement.className = "w-full h-full object-cover rounded-lg";
        videoElement.style.transform = isVideoOff
          ? "brightness(0.2)"
          : "brightness(1)";
        previewContainer.appendChild(videoElement);
      }
    };

    // Update video preview when streaming state changes
    const interval = setInterval(updateVideoPreview, 1000);

    return () => {
      clearInterval(interval);
      if (videoElement) {
        videoElement.remove();
      }
    };
  });

  // Update video brightness when video is toggled
  $effect(() => {
    if (videoElement) {
      videoElement.style.filter = isVideoOff
        ? "brightness(0.2)"
        : "brightness(1)";
    }
  });
</script>

<div
  class="flex-1 bg-base-200 rounded-lg p-4 flex flex-col items-center justify-center"
>
  <!-- Main Video Preview -->
  <div
    class="relative w-full max-w-4xl aspect-video bg-gradient-to-br from-secondary to-primary rounded-lg p-1"
  >
    <div
      class="w-full h-full bg-base-300 rounded-lg flex items-center justify-center relative overflow-hidden"
      bind:this={previewContainer}
    >
      <!-- Fallback content when no video stream -->
      {#if !isStreaming || connectionStatus !== "connected"}
        <div
          class="absolute inset-0 rounded-lg overflow-hidden bg-gradient-to-br from-base-200 to-base-300 flex items-center justify-center"
        >
          <div class="text-center text-base-content/60">
            {#if connectionStatus === "connecting"}
              <div class="flex flex-col items-center gap-4">
                <span class="loading loading-spinner loading-lg"></span>
                <p class="text-lg">Connecting to streaming server...</p>
              </div>
            {:else if connectionStatus === "disconnected"}
              <div class="flex flex-col items-center gap-4">
                <Video size={48} />
                <p class="text-lg">Disconnected from streaming server</p>
                <p class="text-sm opacity-60">
                  Check your connection and try again
                </p>
              </div>
            {:else if !isStreaming}
              <div class="flex flex-col items-center gap-4">
                <Video size={48} />
                <p class="text-lg">Ready to stream</p>
                <p class="text-sm opacity-60">
                  Click "Start Stream" to begin broadcasting
                </p>
              </div>
            {/if}
          </div>
        </div>
      {/if}

      <!-- Stream overlays (only show when streaming) -->
      {#if isStreaming && connectionStatus === "connected"}
        <!-- Main stream overlay -->
        <div
          class="absolute bottom-5 left-5 flex items-center bg-black/50 rounded p-2 backdrop-blur-sm"
        >
          <div class="text-white">
            <p class="text-lg font-bold">You (Host)</p>
            <p class="text-xs">Edify Studio</p>
          </div>
          {#if isMuted}
            <div class="ml-2 badge badge-error badge-xs">MUTED</div>
          {/if}
        </div>

        <!-- Edify Studio watermark -->
        <div
          class="absolute top-5 right-5 bg-black/50 rounded-full px-3 py-1 flex items-center backdrop-blur-sm"
        >
          <div
            class="w-5 h-5 bg-primary rounded-full flex items-center justify-center mr-2"
          >
            <span class="text-primary-content text-xs font-bold">E</span>
          </div>
          <span class="text-white text-sm">Edify Studio</span>
        </div>

        <!-- Live indicator -->
        <div class="absolute top-5 left-5">
          <div class="badge badge-error gap-2">
            <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            LIVE
          </div>
        </div>
      {/if}

      <!-- Resize handles (only when streaming) -->
      {#if isStreaming}
        <div
          class="absolute top-0 right-0 w-2 h-2 bg-white rounded-full transform translate-x-1/2 -translate-y-1/2 cursor-nw-resize"
        ></div>
        <div
          class="absolute top-0 left-0 w-2 h-2 bg-white rounded-full transform -translate-x-1/2 -translate-y-1/2 cursor-ne-resize"
        ></div>
        <div
          class="absolute bottom-0 right-0 w-2 h-2 bg-white rounded-full transform translate-x-1/2 translate-y-1/2 cursor-se-resize"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-2 h-2 bg-white rounded-full transform -translate-x-1/2 translate-y-1/2 cursor-sw-resize"
        ></div>
      {/if}
    </div>
  </div>

  <!-- Layout Selection Icons -->
  <div class="mt-4 flex flex-wrap gap-3 items-center justify-center">
    {#each layouts as layout}
      <div class="tooltip" data-tip="{layout.name} - {layout.useCase}">
        <button
          class="w-16 h-16 border-2 rounded-lg transition-all hover:border-primary {selectedLayout ===
          layout.id
            ? 'border-primary bg-primary/10'
            : 'border-base-300 bg-transparent'} flex items-center justify-center"
          onclick={() => selectLayout(layout.id)}
        >
          <!-- Visual representation of layout inside square border -->
          <div class="w-12 h-12 flex items-center justify-center">
            {#if layout.id === "single"}
              <!-- Single Participant: One large participant view -->
              <div class="w-8 h-6 bg-base-content/20 rounded-xs"></div>
            {:else if layout.id === "horizontal"}
              <!-- Two-Panel Horizontal: Equal halves horizontally -->
              <div class="flex gap-1 w-8 h-6">
                <div class="w-3.5 h-6 bg-base-content/20 rounded-xs"></div>
                <div class="w-3.5 h-6 bg-base-content/20 rounded-xs"></div>
              </div>
            {:else if layout.id === "vertical"}
              <!-- Two-Panel Vertical: Equal halves vertically -->
              <div class="flex flex-col gap-1 w-8 h-6">
                <div class="w-8 h-2.5 bg-base-content/20 rounded-xs"></div>
                <div class="w-8 h-2.5 bg-base-content/20 rounded-xs"></div>
              </div>
            {:else if layout.id === "pip"}
              <!-- Two-Panel PiP: One large + one small floating -->
              <div class="relative w-8 h-6">
                <div class="w-8 h-6 bg-base-content/20 rounded-xs"></div>
                <div
                  class="absolute top-3.5 right-0.5 w-3 h-2 border-2 border-base-100 rounded-xs"
                ></div>
              </div>
            {:else if layout.id === "three"}
              <!-- Three-Panel: 1 portrait + 2 vertical squares -->
              <div class="flex gap-1 w-8 h-6">
                <div class="w-3 h-6 bg-base-content/20 rounded-xs"></div>
                <div class="flex flex-col gap-1 w-4 h-6">
                  <div class="w-4 h-2.5 bg-base-content/20 rounded-xs"></div>
                  <div class="w-4 h-2.5 bg-base-content/20 rounded-xs"></div>
                </div>
              </div>
            {:else if layout.id === "four-vertical"}
              <!-- Four-Panel (Vertical): 1 vertical rectangle + 3 thumbnails -->
              <div class="flex gap-1 w-8 h-6">
                <div class="w-4 h-6 bg-base-content/20 rounded-xs"></div>
                <div class="flex flex-col gap-0.5 w-3 h-6">
                  <div class="w-3 h-2 bg-base-content/20 rounded-xs"></div>
                  <div class="w-3 h-2 bg-base-content/20 rounded-xs"></div>
                  <div class="w-3 h-2 bg-base-content/20 rounded-xs"></div>
                </div>
              </div>
            {:else if layout.id === "four-horizontal"}
              <!-- Four-Panel (Horizontal): 1 horizontal rectangle + 3 thumbnails -->
              <div class="flex flex-col gap-1 w-8 h-6">
                <div class="w-8 h-3 bg-base-content/20 rounded-xs"></div>
                <div class="flex gap-0.5 w-8 h-2">
                  <div class="w-2.5 h-2 bg-base-content/20 rounded-xs"></div>
                  <div class="w-2.5 h-2 bg-base-content/20 rounded-xs"></div>
                  <div class="w-2.5 h-2 bg-base-content/20 rounded-xs"></div>
                </div>
              </div>
            {/if}
          </div>
        </button>
      </div>
    {/each}
  </div>

  <!-- Control Bar -->
  <div class="flex gap-2">
    <div class="join mt-4 bg-base-100 rounded-3xl shadow-lg p-2">
      <button
        class="btn btn-circle btn-sm join-item {isMuted ? 'btn-error' : 'btn-ghost'}"
        onclick={toggleMute}
        title={isMuted ? "Unmute" : "Mute"}
      >
        <Mic class="text-base-content/70" size={30} strokeWidth={1} />
      </button>

      <button
        class="btn btn-circle btn-sm join-item {isVideoOff ? 'btn-error' : 'btn-ghost'}"
        onclick={toggleVideo}
        title={isVideoOff ? "Turn on video" : "Turn off video"}
      >
        <Video class="text-base-content/70" size={30} strokeWidth={1} />
      </button>

      <button
        class="btn btn-circle btn-sm join-item {showSettings
          ? 'btn-active'
          : 'btn-ghost'}"
        onclick={toggleSettings}
        title="Settings"
      >
        <Settings class="text-base-content/70" size={30} strokeWidth={1} />
      </button>

      <button
        class="btn btn-circle btn-sm join-item btn-ghost"
        onclick={() => console.log("Layout options are above the controls")}
        title="Layout options are above the controls"
      >
        <Layout class="text-base-content/70" size={30} strokeWidth={1} />
      </button>

      <button
        class="btn btn-circle btn-sm join-item btn-ghost"
        onclick={openCalendar}
        title="Calendar"
      >
        <Calendar class="text-base-content/70" size={30} strokeWidth={1} />
      </button>
    </div>
    <div class="join mt-4 bg-base-100 rounded-3xl shadow-lg p-2">
      <button
        class="btn btn-circle btn-sm join-item {isMuted ? 'btn-error' : 'btn-ghost'}"
        onclick={toggleMute}
        title={isMuted ? "Unmute" : "Mute"}
      >
        <Mic class="text-base-content/70" size={30} strokeWidth={1} />
      </button>

      <button
        class="btn btn-circle btn-sm join-item {isVideoOff ? 'btn-error' : 'btn-ghost'}"
        onclick={toggleVideo}
        title={isVideoOff ? "Turn on video" : "Turn off video"}
      >
        <Video class="text-base-content/70" size={30} strokeWidth={1} />
      </button>
    </div>
  </div>

  <!-- Settings Panel -->
  {#if showSettings}
    <div class="mt-4 card bg-base-100 shadow-xl w-80">
      <div class="card-body">
        <h3 class="card-title text-sm">Stream Settings</h3>
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text text-sm">4K Quality</span>
            <input
              type="checkbox"
              class="toggle toggle-primary toggle-sm"
              checked={streamingStore.streamQuality === "4K"}
              onchange={handleQualityToggle}
            />
          </label>
        </div>
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text text-sm">Audio Enabled</span>
            <input
              type="checkbox"
              class="toggle toggle-primary toggle-sm"
              checked={!isMuted}
              onchange={toggleMute}
            />
          </label>
        </div>
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text text-sm">Video Enabled</span>
            <input
              type="checkbox"
              class="toggle toggle-primary toggle-sm"
              checked={!isVideoOff}
              onchange={toggleVideo}
            />
          </label>
        </div>
        <div class="card-actions justify-end mt-2">
          <button class="btn btn-primary btn-sm" onclick={toggleSettings}
            >Close</button
          >
        </div>
      </div>
    </div>
  {/if}
</div>
