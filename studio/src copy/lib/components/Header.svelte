<script lang="ts">
  import { Plus, X, ChevronDown, Bell } from "lucide-svelte";
  import { streamingStore } from "$lib/stores/streaming.svelte";

  // Tab management
  let tabs = $state([
    { id: "crypto-trade", title: "Crypto trade with...", isActive: true },
    {
      id: "creative-empowerment",
      title: "Creative-Empowerment...",
      isActive: false,
    },
    {
      id: "conference-sympo",
      title: "The Conference Sympo...",
      isActive: false,
    },
  ]);

  // User profile
  let user = $state({
    name: "<PERSON>",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format",
    notifications: 3,
  });

  // Channels
  let selectedChannel = $state("Main Channel");
  let channels = $state([
    "Main Channel",
    "Gaming Channel",
    "Music Channel",
    "Tech Talks",
  ]);


  // Reactive state from streaming store
  let isStreaming = $derived(streamingStore.isStreaming);
  let connectionStatus = $derived(streamingStore.connectionStatus);

  function addNewTab() {
    const newTab = {
      id: `tab-${Date.now()}`,
      title: `New Stream ${tabs.length + 1}`,
      isActive: false,
    };
    tabs.push(newTab);
  }

  function closeTab(tabId: string, event: Event) {
    event.stopPropagation();
    const index = tabs.findIndex((tab) => tab.id === tabId);
    if (index > -1) {
      tabs.splice(index, 1);

      // If we closed the active tab, make the first remaining tab active
      if (tabs.length > 0 && !tabs.some((tab) => tab.isActive)) {
        tabs[0].isActive = true;
      }
    }
  }

  function selectTab(tabId: string) {
    tabs.forEach((tab) => {
      tab.isActive = tab.id === tabId;
    });
  }

  function selectChannel(channel: string) {
    selectedChannel = channel;
  }

  async function toggleLive() {
    try {
      if (isStreaming) {
        await streamingStore.stopStream();
      } else {
        await streamingStore.startStream("camera");
      }
    } catch (error) {
      console.error("Failed to toggle live stream:", error);
    }
  }
</script>

<div class="navbar bg-base-100 border-b border-base-300 h-16">
  <!-- Tabs Section -->
  <div class="navbar-start flex-1 min-w-0">
    <!-- Tabs -->
    <div class="flex items-center gap-1 flex-1 min-w-0">
      {#each tabs as tab (tab.id)}
        <a
          class="tab tab-bordered {tab.isActive ? 'tab-active' : ''} max-w-48 min-w-0 cursor-pointer"
          onclick={() => selectTab(tab.id)}
          href=" "
        >
          <span class="text-sm truncate flex-1 min-w-0" title={tab.title}>
            {tab.title}
          </span>
          <button
            class="btn btn-ghost btn-xs ml-2"
            onclick={(e) => closeTab(tab.id, e)}
            title="Close tab"
          >
            <X size={12} />
          </button>
        </a>
      {/each}

      <!-- Add Tab Button -->
      <button
        class="btn btn-ghost btn-sm"
        onclick={addNewTab}
        title="Add new tab"
      >
        <Plus size={16} />
      </button>
    </div>
  </div>

  <!-- Right Section -->
  <div class="navbar-end gap-4">
    <!-- Channels Dropdown -->
    <div class="dropdown dropdown-end">
      <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
        <span class="text-sm">Channels</span>
        <ChevronDown size={14} />
      </div>
      <ul class="dropdown-content menu bg-base-100 rounded-box z-[1] w-40 p-2 shadow">
        {#each channels as channel}
          <li>
            <button
              class="{channel === selectedChannel ? 'active' : ''}"
              onclick={() => selectChannel(channel)}
            >
              {channel}
            </button>
          </li>
        {/each}
      </ul>
    </div>

    <!-- Go Live Button -->
    <button
      class="btn {isStreaming ? 'btn-error' : 'btn-primary'} btn-sm"
      onclick={toggleLive}
      disabled={connectionStatus === "connecting"}
    >
      {#if connectionStatus === "connecting"}
        <span class="loading loading-spinner loading-xs"></span>
        Connecting...
      {:else if isStreaming}
        🔴 Live
      {:else}
        Go Live
      {/if}
    </button>

    <!-- Notifications -->
    <div class="indicator">
      {#if user.notifications > 0}
        <span class="indicator-item badge badge-error badge-xs">
          {user.notifications}
        </span>
      {/if}
      <button class="btn btn-ghost btn-sm">
        <Bell size={18} />
      </button>
    </div>

    <!-- User Profile -->
    <div class="dropdown dropdown-end">
      <div tabindex="0" role="button" class="btn btn-ghost btn-sm gap-2">
        <div class="avatar">
          <div class="w-8 h-8 rounded-full">
            <img src={user.avatar} alt={user.name} />
          </div>
        </div>
        <span class="text-sm hidden md:block">{user.name}</span>
        <ChevronDown size={14} class="hidden md:block" />
      </div>
      <ul class="dropdown-content menu bg-base-100 rounded-box z-[1] w-48 p-2 shadow">
        <li class="menu-title">
          <span>{user.name}</span>
          <span class="text-xs text-base-content/70">@joelbahamant</span>
        </li>
        <div class="divider my-1"></div>
        <li><button>Profile Settings</button></li>
        <li><button>Account Settings</button></li>
        <li><button>Billing</button></li>
        <div class="divider my-1"></div>
        <li><button class="text-error">Sign Out</button></li>
      </ul>
    </div>
  </div>
</div>

