import { Connection } from '@kixelated/hang';
import { Broadcast } from '@kixelated/hang/watch';
import type { 
	WatcherConfig, 
	WatcherStatus, 
	ConnectionStatus 
} from '$lib/types/streaming';
import { CONNECTION_CONFIG } from '$lib/constants';

export type { WatcherConfig, WatcherStatus };

export class StudioWatcher {
	private connection: Connection;
	private broadcast: Broadcast;
	private config: WatcherConfig;
	private statusCallbacks: Set<(status: WatcherStatus) => void> = new Set();
	private canvasElement?: HTMLCanvasElement;
	private _isWatching: boolean = false;
	private streamPath: string;

	constructor(config: WatcherConfig) {
		this.config = config;
		this.streamPath = config.streamPath;
		
		// Initialize connection
		this.connection = new Connection({
			url: new URL(config.relayUrl),
			reload: true,
			delay: CONNECTION_CONFIG.retryDelay,
			maxDelay: CONNECTION_CONFIG.maxRetryDelay
		});

		// Initialize broadcast (for watching)
		this.broadcast = new Broadcast(this.connection, {
			path: config.streamPath
		});

		this.setupStatusMonitoring();
	}

	private setupStatusMonitoring() {
		// Monitor connection status changes
		this.connection.status.subscribe((status: ConnectionStatus) => {
			this.notifyStatusChange();
		});
	}

	private notifyStatusChange() {
		const status: WatcherStatus = {
			connectionStatus: this.connection.status.get(),
			isWatching: this._isWatching,
			streamPath: this.streamPath
		};

		this.statusCallbacks.forEach(callback => callback(status));
	}

	// Public API methods
	async startWatching(streamPath?: string): Promise<void> {
		try {
			if (streamPath) {
				this.streamPath = streamPath;
				this.broadcast.path.set(streamPath);
			}
			
			// For now, we'll implement basic watching functionality
			// TODO: Implement actual stream subscription using MoQ consumer
			this._isWatching = true;
			this.notifyStatusChange();
			
			console.log(`Started watching stream: ${this.streamPath}`);
		} catch (error) {
			console.error('Failed to start watching:', error);
			throw error;
		}
	}

	async stopWatching(): Promise<void> {
		try {
			this._isWatching = false;
			this.notifyStatusChange();
			
			console.log(`Stopped watching stream: ${this.streamPath}`);
		} catch (error) {
			console.error('Failed to stop watching:', error);
			throw error;
		}
	}

	setStreamPath(path: string): void {
		this.streamPath = path;
		this.broadcast.path.set(path);
		this.notifyStatusChange();
	}

	// Canvas integration for video rendering
	attachCanvas(canvas: HTMLCanvasElement): void {
		this.canvasElement = canvas;
		console.log('Canvas attached for video rendering');
	}

	detachCanvas(): void {
		if (this.canvasElement) {
			this.canvasElement = undefined;
			console.log('Canvas detached');
		}
	}

	// Status monitoring
	onStatusChange(callback: (status: WatcherStatus) => void): () => void {
		this.statusCallbacks.add(callback);
		
		// Call immediately with current status
		callback({
			connectionStatus: this.connection.status.get(),
			isWatching: this._isWatching,
			streamPath: this.streamPath
		});

		// Return unsubscribe function
		return () => {
			this.statusCallbacks.delete(callback);
		};
	}

	// Get current status
	getStatus(): WatcherStatus {
		return {
			connectionStatus: this.connection.status.get(),
			isWatching: this._isWatching,
			streamPath: this.streamPath
		};
	}

	// Get canvas element if attached
	getCanvas(): HTMLCanvasElement | undefined {
		return this.canvasElement;
	}

	// Get stream path
	getStreamPath(): string {
		return this.streamPath;
	}

	// Check if currently watching
	isWatching(): boolean {
		return this._isWatching;
	}

	// Cleanup
	destroy(): void {
		this.detachCanvas();
		this.broadcast.close();
		this.connection.close();
		this.statusCallbacks.clear();
		this._isWatching = false;
	}
}