<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import Header from '$lib/components/Header.svelte';
	import StatusBar from '$lib/components/StatusBar.svelte';
	import { StudioWatcher, type WatcherStatus } from '$lib/services/moq-watcher';
	import { checkSupport, getSupportErrorMessage } from '$lib/services/support';
	import type { SupportCheckResult } from '$lib/types/support';
	import { STREAMING_CONFIG } from '$lib/config/streaming';
	
	// State
	let watcher: StudioWatcher | undefined = $state();
	let status: WatcherStatus | undefined = $state();
	let supportResult: SupportCheckResult | undefined = $state();
	let canvasElement: HTMLCanvasElement;
	let canvasContainer: HTMLDivElement;
	
	// Controls
	let isWatching = $state(false);
	let connectionStatus = $state<'connecting' | 'connected' | 'disconnected' | 'unsupported'>('disconnected');
	let streamPath = $state('demo/publisher'); // Watch the publisher demo by default
	let isMuted = $state(false);
	let volume = $state(50);
	
	// Logs
	let logs = $state<string[]>([]);
	
	function addLog(message: string) {
		const timestamp = new Date().toLocaleTimeString();
		logs = [`[${timestamp}] ${message}`, ...logs.slice(0, 49)]; // Keep last 50 logs
	}
	
	async function initializeWatcher() {
		try {
			addLog('Initializing watcher...');
			
			watcher = new StudioWatcher({
				relayUrl: STREAMING_CONFIG.relay.url,
				streamPath,
				muted: isMuted
			});
			
			// Subscribe to status changes
			watcher.onStatusChange((newStatus) => {
				status = newStatus;
				isWatching = newStatus.isWatching;
				connectionStatus = newStatus.connectionStatus;
				
				addLog(`Status: ${newStatus.connectionStatus}, Watching: ${newStatus.isWatching}`);
			});
			
			// Attach canvas for video rendering
			if (canvasElement) {
				watcher.attachCanvas(canvasElement);
				addLog('Canvas attached for video rendering');
			}
			
			addLog('Watcher initialized successfully');
		} catch (error) {
			addLog(`Failed to initialize watcher: ${error}`);
			console.error(error);
		}
	}
	
	function setupCanvas() {
		if (canvasContainer && !canvasElement) {
			canvasElement = document.createElement('canvas');
			canvasElement.className = 'w-full h-full object-cover rounded-lg';
			canvasElement.width = 1280;
			canvasElement.height = 720;
			canvasContainer.appendChild(canvasElement);
			
			// Draw placeholder content
			const ctx = canvasElement.getContext('2d');
			if (ctx) {
				ctx.fillStyle = '#1f2937';
				ctx.fillRect(0, 0, canvasElement.width, canvasElement.height);
				ctx.fillStyle = '#6b7280';
				ctx.font = '48px sans-serif';
				ctx.textAlign = 'center';
				ctx.fillText('Waiting for stream...', canvasElement.width / 2, canvasElement.height / 2);
			}
		}
	}
	
	async function startWatching() {
		if (!watcher) {
			addLog('Watcher not initialized');
			return;
		}
		
		try {
			addLog(`Starting to watch stream: ${streamPath}`);
			await watcher.startWatching(streamPath);
		} catch (error) {
			addLog(`Failed to start watching: ${error}`);
		}
	}
	
	async function stopWatching() {
		if (!watcher) return;
		
		try {
			addLog('Stopping watcher...');
			await watcher.stopWatching();
		} catch (error) {
			addLog(`Failed to stop watching: ${error}`);
		}
	}
	
	function changeStreamPath() {
		if (watcher) {
			watcher.setStreamPath(streamPath);
			addLog(`Stream path changed to: ${streamPath}`);
		}
	}
	
	function clearLogs() {
		logs = [];
	}
	
	onMount(async () => {
		addLog('Checking browser support...');
		
		try {
			supportResult = await checkSupport('watch');
			addLog(`Support level: ${supportResult.overall}`);
			
			if (supportResult.overall === 'none') {
				addLog(`Error: ${getSupportErrorMessage(supportResult)}`);
				return;
			}
			
			setupCanvas();
			await initializeWatcher();
		} catch (error) {
			addLog(`Support check failed: ${error}`);
		}
	});
	
	onDestroy(() => {
		watcher?.destroy();
	});
</script>

<svelte:head>
	<title>MoQ Watcher Demo - Edify Studio</title>
</svelte:head>

<div class="w-full min-h-screen bg-base-200 flex flex-col">
	
	<!-- Main Content -->
	<div class="container mx-auto p-4 max-w-6xl flex-1">
		<div class="flex items-center gap-4 mb-6">
			<a href="/" class="btn btn-ghost">← Back to Studio</a>
			<h1 class="text-3xl font-bold">MoQ Watcher Demo</h1>
			<div class="badge {supportResult?.overall === 'full' ? 'badge-success' : supportResult?.overall === 'partial' ? 'badge-warning' : 'badge-error'}">
				{supportResult?.overall || 'checking...'}
			</div>
		</div>
		
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Video Display -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">Stream Viewer</h2>
					<div class="relative w-full aspect-video bg-base-300 rounded-lg overflow-hidden" bind:this={canvasContainer}>
						<!-- Canvas will be inserted here -->
					</div>
					
					<!-- Status indicators -->
					<div class="flex gap-2 mt-4">
						<div class="badge {connectionStatus === 'connected' ? 'badge-success' : connectionStatus === 'connecting' ? 'badge-warning' : 'badge-error'}">
							{connectionStatus}
						</div>
						{#if isWatching}
							<div class="badge badge-success">WATCHING</div>
						{/if}
						{#if isMuted}
							<div class="badge badge-warning">🔇 MUTED</div>
						{/if}
					</div>
					
					<!-- Audio Controls -->
					{#if isWatching}
						<div class="flex items-center gap-4 mt-4">
							<button class="btn btn-circle btn-sm" onclick={() => isMuted = !isMuted}>
								{isMuted ? '🔇' : '🔊'}
							</button>
							<input 
								type="range" 
								min="0" 
								max="100" 
								bind:value={volume} 
								class="range range-sm flex-1" 
								disabled={isMuted}
							/>
							<span class="text-sm w-12">{volume}%</span>
						</div>
					{/if}
				</div>
			</div>
			
			<!-- Controls -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">Watcher Controls</h2>
					
					<!-- Stream Path -->
					<div class="form-control">
						<label for="streamPath" class="label">
							<span class="label-text">Stream Path to Watch</span>
						</label>
						<input
							id="streamPath"
							type="text"
							bind:value={streamPath}
							onchange={changeStreamPath}
							class="input input-bordered"
							disabled={isWatching}
						/>
						<div class="label">
							<span class="label-text-alt">Enter the stream path you want to watch</span>
						</div>
					</div>
					
					<!-- Quick Stream Paths -->
					<div class="form-control">
						<div class="label">
							<span class="label-text">Quick Select</span>
						</div>
						<div class="flex gap-2 flex-wrap">
							<button 
								class="btn btn-outline btn-sm" 
								onclick={() => { streamPath = 'demo/publisher'; changeStreamPath(); }}
								disabled={isWatching}
							>
								Publisher Demo
							</button>
							<button 
								class="btn btn-outline btn-sm" 
								onclick={() => { streamPath = 'studio/host'; changeStreamPath(); }}
								disabled={isWatching}
							>
								Studio Host
							</button>
							<button 
								class="btn btn-outline btn-sm" 
								onclick={() => { streamPath = 'test/stream'; changeStreamPath(); }}
								disabled={isWatching}
							>
								Test Stream
							</button>
						</div>
					</div>
					
					<!-- Watch Controls -->
					<div class="flex gap-2 mt-4">
						{#if !isWatching}
							<button class="btn btn-success flex-1" onclick={startWatching} disabled={connectionStatus !== 'connected' && connectionStatus !== 'disconnected'}>
								Start Watching
							</button>
						{:else}
							<button class="btn btn-error flex-1" onclick={stopWatching}>
								Stop Watching
							</button>
						{/if}
					</div>
					
					<!-- Connection Info -->
					<div class="mt-4 p-4 bg-base-200 rounded">
						<h3 class="font-bold text-sm mb-2">Connection Info</h3>
						<div class="text-xs space-y-1">
							<div>Relay: <span class="font-mono">{STREAMING_CONFIG.relay.url}</span></div>
							<div>Stream: <span class="font-mono">{streamPath}</span></div>
							<div>Status: <span class="font-mono">{connectionStatus}</span></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Instructions -->
		<div class="alert alert-info mt-6">
			<div>
				<h3 class="font-bold">How to test:</h3>
				<ol class="list-decimal list-inside mt-2 flex flex-col gap-1">
					<li>First, open the <a href="/demo/publisher" class="link">Publisher Demo</a> in another tab</li>
					<li>Start a stream in the publisher with the path "demo/publisher"</li>
					<li>Come back to this tab and click "Start Watching"</li>
					<li>You should see the stream from the publisher appear here</li>
				</ol>
			</div>
		</div>
		
		<!-- Logs -->
		<div class="card bg-base-100 shadow-xl mt-6">
			<div class="card-body">
				<div class="flex items-center justify-between">
					<h2 class="card-title">Event Logs</h2>
					<button class="btn btn-ghost btn-sm" onclick={clearLogs}>Clear</button>
				</div>
				<div class="bg-base-300 rounded p-4 h-48 overflow-y-auto font-mono text-sm">
					{#each logs as log}
						<div class="mb-1">{log}</div>
					{/each}
					{#if logs.length === 0}
						<div class="text-base-content/60">No logs yet...</div>
					{/if}
				</div>
			</div>
		</div>
		
		<!-- Support Details -->
		{#if supportResult}
			<div class="card bg-base-100 shadow-xl mt-6">
				<div class="card-body">
					<h2 class="card-title">Browser Support Details</h2>
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div class="stat">
							<div class="stat-title">Overall</div>
							<div class="stat-value text-lg {supportResult.overall === 'full' ? 'text-success' : supportResult.overall === 'partial' ? 'text-warning' : 'text-error'}">
								{supportResult.overall}
							</div>
						</div>
						<div class="stat">
							<div class="stat-title">WebTransport</div>
							<div class="stat-value text-lg {supportResult.details.webtransport ? 'text-success' : 'text-error'}">
								{supportResult.details.webtransport ? 'Yes' : 'No'}
							</div>
						</div>
						<div class="stat">
							<div class="stat-title">Watching</div>
							<div class="stat-value text-lg {supportResult.watch === 'full' ? 'text-success' : supportResult.watch === 'partial' ? 'text-warning' : 'text-error'}">
								{supportResult.watch}
							</div>
						</div>
					</div>
					
					{#if supportResult.warnings.length > 0}
						<div class="alert alert-warning mt-4">
							<h3 class="font-bold">Warnings:</h3>
							<ul>
								{#each supportResult.warnings as warning}
									<li>• {warning}</li>
								{/each}
							</ul>
						</div>
					{/if}
					
					{#if supportResult.recommendations.length > 0}
						<div class="alert alert-info mt-4">
							<h3 class="font-bold">Recommendations:</h3>
							<ul>
								{#each supportResult.recommendations as rec}
									<li>• {rec}</li>
								{/each}
							</ul>
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
	
	<!-- Status Bar -->
	<StatusBar />
</div>