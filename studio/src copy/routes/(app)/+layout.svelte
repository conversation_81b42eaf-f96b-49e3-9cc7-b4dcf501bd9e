<script lang="ts">

  let { children } = $props();
  
  let currentTheme = $state<string>('dark');
  
  function toggleTheme(): void {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
  }
</script>

<div data-theme={currentTheme} class="min-h-screen text-base-content/70">
  <!-- Global Theme Toggle -->
  <div class="absolute top-4 right-4 z-50">
    <button 
      class="btn btn-circle btn-ghost btn-sm"
      onclick={toggleTheme}
      title="Toggle theme"
    >
      {currentTheme === 'light' ? '🌙' : '☀️'}
    </button>
  </div>
  
  <!-- Page Content -->
  {@render children()}
</div>
