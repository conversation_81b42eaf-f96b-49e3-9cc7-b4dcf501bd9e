# Studio Essentials UI Documentation
*Comprehensive implementation guide for Evmux clone core streaming interface*

## Overview
This document provides detailed specifications for implementing the Studio Essentials components based on the Evmux tutorial analysis. These are the core streaming interface components that form the foundation of professional live streaming functionality.

---

## 1. Video Clips Management Panel

### Purpose
Allow users to load, preview, and play video clips during live streams with seamless transitions back to the main scene.

### Visual Design
- Compact panel with video thumbnail grid
- Preview on hover functionality
- Play state indicators (red dot when active)
- Clean, minimal interface with upload capability

### Interaction Patterns
- **Mouse hover**: Show video preview/scrub
- **Single click**: Play video instantly
- **Auto-return**: Return to previous scene when video ends
- **Visual feedback**: Red dot indicator during playback
- **Upload**: Plus button to add new video clips

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-header flex justify-between items-center p-3 border-b border-base-200">
    <h3 class="card-title text-sm font-semibold">Videos</h3>
    <button class="btn btn-sm btn-ghost btn-square tooltip" data-tip="Add video clip">
      <Plus size="16" />
    </button>
  </div>
  <div class="card-body p-3 space-y-2">
    {#each videos as video, index}
      <div class="relative group">
        <div 
          class="aspect-video bg-base-200 rounded overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all"
          on:click={() => playVideo(video)}
          on:mouseenter={() => previewVideo(video)}
          on:mouseleave={() => stopPreview()}>
          
          <img 
            src={video.thumbnail} 
            alt={video.name} 
            class="w-full h-full object-cover transition-opacity"
            class:opacity-80={video.isPlaying} />
          
          <!-- Play indicator -->
          {#if video.isPlaying}
            <div class="absolute top-2 right-2 flex items-center gap-1">
              <div class="w-2 h-2 bg-error rounded-full animate-pulse"></div>
              <span class="text-xs bg-error text-error-content px-1 rounded">LIVE</span>
            </div>
          {/if}
          
          <!-- Play button overlay on hover -->
          <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button class="btn btn-circle btn-primary btn-sm">
              <Play size="16" />
            </button>
          </div>
        </div>
        
        <p class="text-xs mt-1 truncate font-medium">{video.name}</p>
        <p class="text-xs text-base-content/60">{video.duration}</p>
      </div>
    {/each}
    
    <!-- Empty state -->
    {#if videos.length === 0}
      <div class="text-center py-8">
        <div class="text-base-content/40 mb-2">
          <Video size="32" class="mx-auto" />
        </div>
        <p class="text-xs text-base-content/60">No video clips yet</p>
        <button class="btn btn-sm btn-primary mt-2">Add Video</button>
      </div>
    {/if}
  </div>
</div>
```

### Component Properties
```typescript
interface VideoClip {
  id: string;
  name: string;
  thumbnail: string;
  duration: string;
  filePath: string;
  isPlaying: boolean;
}
```

---

## 2. Overlays Management Panel

### Purpose
Manage transparent overlays, full-screen graphics, and layout-specific overlays for professional stream branding.

### Visual Design
- Grid layout for overlay thumbnails (2 columns)
- Active state indication with primary border
- Quick toggle functionality
- Support for different overlay types (transparent, full-screen, layout-specific)

### Interaction Patterns
- **Single click**: Toggle overlay on/off
- **Visual feedback**: Border color change and "ON" badge for active overlays
- **Category support**: Different overlay types for different layouts
- **Instant preview**: Immediate application to stream

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-header flex justify-between items-center p-3 border-b border-base-200">
    <h3 class="card-title text-sm font-semibold">Overlays</h3>
    <button class="btn btn-sm btn-ghost btn-square tooltip" data-tip="Add overlay">
      <Plus size="16" />
    </button>
  </div>
  <div class="card-body p-3">
    <!-- Category tabs -->
    <div class="tabs tabs-boxed tabs-xs mb-3">
      <button class="tab tab-active">All</button>
      <button class="tab">Transparent</button>
      <button class="tab">Full Screen</button>
      <button class="tab">Layout</button>
    </div>
    
    <div class="grid grid-cols-2 gap-2">
      {#each overlays as overlay}
        <div class="relative">
          <button 
            class="aspect-video w-full bg-base-200 rounded overflow-hidden border-2 transition-all hover:scale-105"
            class:border-primary={overlay.isActive}
            class:border-base-300={!overlay.isActive}
            class:shadow-lg={overlay.isActive}
            on:click={() => toggleOverlay(overlay)}>
            
            <img 
              src={overlay.thumbnail} 
              alt={overlay.name} 
              class="w-full h-full object-cover" />
            
            <!-- Active indicator -->
            {#if overlay.isActive}
              <div class="absolute inset-0 bg-primary/20 flex items-center justify-center">
                <div class="badge badge-primary badge-sm font-bold">ON</div>
              </div>
            {/if}
            
            <!-- Overlay type badge -->
            <div class="absolute top-1 left-1">
              <div class="badge badge-xs badge-outline bg-base-100/80">
                {overlay.type}
              </div>
            </div>
          </button>
          
          <p class="text-xs mt-1 truncate text-center font-medium">{overlay.name}</p>
        </div>
      {/each}
    </div>
    
    <!-- Quick actions -->
    <div class="flex gap-2 mt-3">
      <button class="btn btn-xs btn-outline flex-1" on:click={hideAllOverlays}>
        Hide All
      </button>
      <button class="btn btn-xs btn-outline flex-1" on:click={showPresets}>
        Presets
      </button>
    </div>
  </div>
</div>
```

### Component Properties
```typescript
interface Overlay {
  id: string;
  name: string;
  thumbnail: string;
  type: 'transparent' | 'fullscreen' | 'layout';
  filePath: string;
  isActive: boolean;
  layoutCompatibility: string[];
}
```

---

## 3. Background Management Panel

### Purpose
Switch between different background images/videos for the streaming scene with seamless transitions.

### Visual Design
- Single column layout for background options
- Larger thumbnails to showcase background details
- Clear active state indication
- Upload functionality for custom backgrounds

### Interaction Patterns
- **Single click**: Set as active background
- **Upload support**: Add custom background assets
- **Preview**: Immediate application to stream preview
- **Categories**: Support for different background types

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-header flex justify-between items-center p-3 border-b border-base-200">
    <h3 class="card-title text-sm font-semibold">Backgrounds</h3>
    <button class="btn btn-sm btn-ghost btn-square tooltip" data-tip="Add background">
      <Plus size="16" />
    </button>
  </div>
  <div class="card-body p-3">
    <div class="space-y-3">
      {#each backgrounds as background}
        <div class="relative">
          <button 
            class="w-full aspect-video bg-base-200 rounded overflow-hidden border-2 transition-all hover:scale-[1.02]"
            class:border-primary={background.isActive}
            class:border-base-300={!background.isActive}
            class:shadow-md={background.isActive}
            on:click={() => setBackground(background)}>
            
            <img 
              src={background.thumbnail} 
              alt={background.name} 
              class="w-full h-full object-cover" />
            
            <!-- Active indicator -->
            {#if background.isActive}
              <div class="absolute inset-0 bg-primary/30 flex items-center justify-center">
                <div class="badge badge-primary badge-lg font-bold">ACTIVE</div>
              </div>
            {/if}
            
            <!-- Background type indicator -->
            {#if background.type === 'video'}
              <div class="absolute bottom-2 left-2">
                <div class="badge badge-xs badge-accent">Video</div>
              </div>
            {/if}
          </button>
          
          <div class="flex justify-between items-center mt-2">
            <p class="text-xs font-medium truncate flex-1">{background.name}</p>
            <button class="btn btn-ghost btn-xs" on:click|stopPropagation={() => editBackground(background)}>
              <MoreHorizontal size="12" />
            </button>
          </div>
        </div>
      {/each}
    </div>
  </div>
</div>
```

### Component Properties
```typescript
interface Background {
  id: string;
  name: string;
  thumbnail: string;
  type: 'image' | 'video';
  filePath: string;
  isActive: boolean;
}
```

---

## 4. Comments Display & Management

### Purpose
Show live comments from streaming platforms with customizable styling and real-time display.

### Visual Design
- Chat bubble interface with different style options
- Brand customization controls
- Scrollable comment feed
- Style picker with preview

### Interaction Patterns
- **Style selection**: Choose from 4 different comment styles
- **Color customization**: Primary and secondary color pickers
- **Real-time display**: Live comment feed from platforms
- **Moderation**: Hide/show specific comments

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-header flex justify-between items-center p-3 border-b border-base-200">
    <h3 class="card-title text-sm font-semibold">Comments</h3>
    <div class="dropdown dropdown-end">
      <button class="btn btn-sm btn-ghost btn-square" tabindex="0">
        <Settings size="16" />
      </button>
      <div class="dropdown-content menu bg-base-100 rounded-box w-64 p-3 shadow-lg border border-base-200">
        <!-- Style Selection -->
        <div class="form-control mb-3">
          <label class="label">
            <span class="label-text text-xs font-semibold">Comment Style</span>
          </label>
          <div class="grid grid-cols-2 gap-2">
            {#each commentStyles as style, index}
              <button 
                class="btn btn-xs"
                class:btn-primary={selectedCommentStyle === index}
                class:btn-outline={selectedCommentStyle !== index}
                on:click={() => selectedCommentStyle = index}>
                Style {index + 1}
              </button>
            {/each}
          </div>
        </div>
        
        <!-- Color Customization -->
        <div class="form-control mb-3">
          <label class="label">
            <span class="label-text text-xs font-semibold">Colors</span>
          </label>
          <div class="flex gap-2">
            <div class="form-control flex-1">
              <input 
                type="color" 
                class="w-full h-8 rounded border border-base-300" 
                bind:value={commentColors.background} />
              <span class="text-xs mt-1">Background</span>
            </div>
            <div class="form-control flex-1">
              <input 
                type="color" 
                class="w-full h-8 rounded border border-base-300" 
                bind:value={commentColors.text} />
              <span class="text-xs mt-1">Text</span>
            </div>
          </div>
        </div>
        
        <!-- Frame Options -->
        <div class="form-control">
          <label class="label">
            <span class="label-text text-xs font-semibold">Frame</span>
          </label>
          <select class="select select-xs">
            <option>None</option>
            <option>Simple</option>
            <option>Rounded</option>
            <option>Shadow</option>
          </select>
        </div>
      </div>
    </div>
  </div>
  
  <div class="card-body p-3">
    <!-- Comment Feed -->
    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
      {#each comments as comment}
        <div class="chat chat-start">
          <div class="chat-image avatar">
            <div class="w-6 rounded-full">
              <img src={comment.avatar || '/default-avatar.png'} alt={comment.author} />
            </div>
          </div>
          <div class="chat-header text-xs">
            {comment.author}
            <time class="text-xs opacity-50">{comment.timestamp}</time>
          </div>
          <div 
            class="chat-bubble text-xs max-w-xs"
            style="background-color: {commentColors.background}; color: {commentColors.text}">
            {comment.message}
          </div>
          <!-- Platform badge -->
          <div class="chat-footer opacity-50">
            <div class="badge badge-xs">{comment.platform}</div>
          </div>
        </div>
      {/each}
    </div>
    
    <!-- Empty state -->
    {#if comments.length === 0}
      <div class="text-center py-6">
        <MessageCircle size="24" class="mx-auto text-base-content/40 mb-2" />
        <p class="text-xs text-base-content/60">No comments yet</p>
        <p class="text-xs text-base-content/40">Comments will appear here when you go live</p>
      </div>
    {/if}
    
    <!-- Quick actions -->
    <div class="flex gap-2 mt-3">
      <button class="btn btn-xs btn-outline flex-1" on:click={clearComments}>
        Clear All
      </button>
      <button class="btn btn-xs btn-outline flex-1" on:click={pauseComments}>
        {commentsPaused ? 'Resume' : 'Pause'}
      </button>
    </div>
  </div>
</div>

<style>
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.2);
    border-radius: 2px;
  }
</style>
```

### Component Properties
```typescript
interface Comment {
  id: string;
  author: string;
  message: string;
  timestamp: string;
  platform: 'youtube' | 'facebook' | 'twitch' | 'linkedin';
  avatar?: string;
}

interface CommentStyle {
  id: number;
  name: string;
  backgroundOpacity: number;
  borderRadius: string;
  animation: boolean;
}
```

---

## 5. Banner Creation & Management

### Purpose
Create and display scrolling tickers and static banners with customizable text and styling.

### Visual Design
- Banner list with preview and controls
- Creation modal with primary/secondary text fields
- Ticker toggle option
- Active state management

### Interaction Patterns
- **Create banner**: Modal interface with text inputs
- **Toggle display**: On/off buttons for each banner
- **Ticker mode**: Scrolling vs static display
- **Edit functionality**: Modify existing banners

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-header flex justify-between items-center p-3 border-b border-base-200">
    <h3 class="card-title text-sm font-semibold">Banners</h3>
    <button 
      class="btn btn-sm btn-primary"
      on:click={() => showCreateBanner = true}>
      <Plus size="16" />
      Create
    </button>
  </div>
  
  <div class="card-body p-3 space-y-2">
    {#each banners as banner}
      <div class="border border-base-200 rounded-lg p-3 hover:bg-base-50 transition-colors">
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <p class="text-sm font-semibold truncate">{banner.primaryText}</p>
            {#if banner.secondaryText}
              <p class="text-xs text-base-content/60 truncate mt-1">{banner.secondaryText}</p>
            {/if}
            
            <!-- Banner properties -->
            <div class="flex items-center gap-2 mt-2">
              {#if banner.isTicker}
                <div class="badge badge-accent badge-xs">
                  <ArrowRight size="10" class="mr-1" />
                  Ticker
                </div>
              {/if}
              <div class="badge badge-outline badge-xs">
                {banner.position}
              </div>
            </div>
          </div>
          
          <div class="flex flex-col gap-1 ml-2">
            <button 
              class="btn btn-xs"
              class:btn-primary={banner.isActive}
              class:btn-outline={!banner.isActive}
              on:click={() => toggleBanner(banner)}>
              {banner.isActive ? 'ON' : 'OFF'}
            </button>
            
            <div class="dropdown dropdown-end">
              <button class="btn btn-xs btn-ghost" tabindex="0">
                <MoreHorizontal size="12" />
              </button>
              <ul class="dropdown-content menu bg-base-100 rounded-box w-32 p-2 shadow">
                <li><button on:click={() => editBanner(banner)}>Edit</button></li>
                <li><button on:click={() => duplicateBanner(banner)}>Duplicate</button></li>
                <li><button on:click={() => deleteBanner(banner)} class="text-error">Delete</button></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    {/each}
    
    <!-- Empty state -->
    {#if banners.length === 0}
      <div class="text-center py-8">
        <Type size="32" class="mx-auto text-base-content/40 mb-2" />
        <p class="text-xs text-base-content/60 mb-2">No banners created yet</p>
        <button 
          class="btn btn-sm btn-primary"
          on:click={() => showCreateBanner = true}>
          Create First Banner
        </button>
      </div>
    {/if}
  </div>
</div>

<!-- Banner Creation/Edit Modal -->
{#if showCreateBanner || editingBanner}
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">
        {editingBanner ? 'Edit Banner' : 'Create Banner'}
      </h3>
      
      <div class="space-y-4">
        <!-- Primary Text -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-semibold">Primary Text</span>
            <span class="label-text-alt">{newBanner.primaryText.length}/100</span>
          </label>
          <input 
            type="text" 
            class="input input-bordered" 
            maxlength="100"
            placeholder="Enter main banner text..."
            bind:value={newBanner.primaryText} />
        </div>
        
        <!-- Secondary Text -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Secondary Text (Optional)</span>
            <span class="label-text-alt">{newBanner.secondaryText.length}/100</span>
          </label>
          <input 
            type="text" 
            class="input input-bordered" 
            maxlength="100"
            placeholder="Enter secondary text..."
            bind:value={newBanner.secondaryText} />
        </div>
        
        <!-- Banner Options -->
        <div class="grid grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Position</span>
            </label>
            <select class="select select-bordered" bind:value={newBanner.position}>
              <option value="top">Top</option>
              <option value="bottom">Bottom</option>
              <option value="middle">Middle</option>
            </select>
          </div>
          
          <div class="form-control">
            <label class="cursor-pointer label">
              <span class="label-text">Scrolling Ticker</span>
              <input 
                type="checkbox" 
                class="toggle toggle-primary" 
                bind:checked={newBanner.isTicker} />
            </label>
          </div>
        </div>
        
        <!-- Preview -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Preview</span>
          </label>
          <div class="bg-base-200 rounded p-4 min-h-16 flex items-center">
            <div class="w-full">
              <div class="text-sm font-semibold">{newBanner.primaryText || 'Primary text will appear here'}</div>
              {#if newBanner.secondaryText}
                <div class="text-xs text-base-content/60 mt-1">{newBanner.secondaryText}</div>
              {/if}
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-action">
        <button 
          class="btn btn-primary" 
          disabled={!newBanner.primaryText.trim()}
          on:click={editingBanner ? updateBanner : createBanner}>
          {editingBanner ? 'Update' : 'Create'} Banner
        </button>
        <button 
          class="btn" 
          on:click={() => {
            showCreateBanner = false;
            editingBanner = null;
            resetNewBanner();
          }}>
          Cancel
        </button>
      </div>
    </div>
  </div>
{/if}
```

### Component Properties
```typescript
interface Banner {
  id: string;
  primaryText: string;
  secondaryText?: string;
  position: 'top' | 'bottom' | 'middle';
  isTicker: boolean;
  isActive: boolean;
  style: BannerStyle;
}

interface BannerStyle {
  backgroundColor: string;
  textColor: string;
  fontSize: string;
  borderRadius: string;
}
```

---

## 6. Music & Sound Effects Panel

### Purpose
Control background music playlists and trigger sound effects with volume control and playlist management.

### Visual Design
- Split panel for music and sound effects
- Volume controls with range sliders
- Sound effect button grid
- Playlist management interface

### Interaction Patterns
- **Music toggle**: On/off for background music
- **Volume control**: Individual volume sliders
- **SFX triggers**: Instant sound effect playback
- **Playlist management**: Edit music list with upload capability

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-header p-3 border-b border-base-200">
    <h3 class="card-title text-sm font-semibold">Music & SFX</h3>
  </div>
  
  <div class="card-body p-3">
    <!-- Background Music Section -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center gap-2">
          <Music size="16" class="text-base-content/60" />
          <span class="text-sm font-semibold">Background Music</span>
        </div>
        <button 
          class="btn btn-xs"
          class:btn-primary={musicPlaying}
          class:btn-outline={!musicPlaying}
          on:click={toggleMusic}>
          {musicPlaying ? 'ON' : 'OFF'}
        </button>
      </div>
      
      <!-- Currently Playing -->
      {#if currentTrack}
        <div class="bg-base-200 rounded p-2 mb-3">
          <div class="flex items-center justify-between">
            <div class="min-w-0 flex-1">
              <p class="text-xs font-medium truncate">{currentTrack.name}</p>
              <p class="text-xs text-base-content/60">{currentTrack.artist}</p>
            </div>
            <div class="text-xs text-base-content/60">
              {formatTime(currentTrack.currentTime)} / {formatTime(currentTrack.duration)}
            </div>
          </div>
          
          <!-- Progress bar -->
          <progress 
            class="progress progress-primary w-full h-1 mt-2" 
            value={currentTrack.currentTime} 
            max={currentTrack.duration}></progress>
        </div>
      {/if}
      
      <!-- Volume Control -->
      <div class="form-control mb-3">
        <label class="label">
          <span class="label-text text-xs">Volume</span>
          <span class="label-text-alt text-xs">{musicVolume}%</span>
        </label>
        <input 
          type="range" 
          class="range range-primary range-xs" 
          min="0" 
          max="100" 
          bind:value={musicVolume} />
      </div>
      
      <!-- Playlist Controls -->
      <div class="flex gap-2">
        <button 
          class="btn btn-xs btn-outline flex-1" 
          on:click={() => showMusicList = true}>
          <List size="12" />
          Playlist ({musicTracks.length})
        </button>
        <button 
          class="btn btn-xs btn-outline" 
          on:click={shufflePlaylist}>
          <Shuffle size="12" />
        </button>
        <button 
          class="btn btn-xs btn-outline" 
          on:click={toggleLoop}>
          <RotateCcw size="12" class:text-primary={loopEnabled} />
        </button>
      </div>
    </div>

    <!-- Sound Effects Section -->
    <div>
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center gap-2">
          <Volume2 size="16" class="text-base-content/60" />
          <span class="text-sm font-semibold">Sound Effects</span>
        </div>
        <button 
          class="btn btn-xs btn-ghost" 
          on:click={() => showAddSFX = true}>
          <Plus size="12" />
        </button>
      </div>
      
      <!-- SFX Grid -->
      <div class="grid grid-cols-2 gap-2 mb-3">
        {#each soundEffects as sfx}
          <button 
            class="btn btn-sm btn-outline text-xs relative group"
            on:click={() => playSoundEffect(sfx)}
            disabled={sfx.isPlaying}>
            
            {#if sfx.isPlaying}
              <span class="loading loading-spinner loading-xs"></span>
            {:else}
              <Play size="12" />
            {/if}
            
            <span class="truncate">{sfx.name}</span>
            
            <!-- Volume indicator -->
            <div class="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
              <div class="badge badge-xs">{sfx.volume}%</div>
            </div>
          </button>
        {/each}
      </div>
      
      <!-- Master SFX Volume -->
      <div class="form-control">
        <label class="label">
          <span class="label-text text-xs">SFX Volume</span>
          <span class="label-text-alt text-xs">{sfxVolume}%</span>
        </label>
        <input 
          type="range" 
          class="range range-accent range-xs" 
          min="0" 
          max="100" 
          bind:value={sfxVolume} />
      </div>
    </div>
  </div>
</div>

<!-- Music List Modal -->
{#if showMusicList}
  <div class="modal modal-open">
    <div class="modal-box max-w-md">
      <h3 class="font-bold text-lg mb-4">Music Playlist</h3>
      
      <div class="space-y-2 max-h-64 overflow-y-auto">
        {#each musicTracks as track, index}
          <div 
            class="flex items-center justify-between p-2 rounded hover:bg-base-200"
            class:bg-primary/10={track.id === currentTrack?.id}>
            
            <div class="flex items-center gap-2 min-w-0 flex-1">
              <button 
                class="btn btn-xs btn-ghost btn-square"
                on:click={() => playTrack(track)}>
                {#if track.id === currentTrack?.id && musicPlaying}
                  <Pause size="12" />
                {:else}
                  <Play size="12" />
                {/if}
              </button>
              
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium truncate">{track.name}</p>
                <p class="text-xs text-base-content/60 truncate">{track.artist} • {track.duration}</p>
              </div>
            </div>
            
            <div class="flex items-center gap-1">
              <button 
                class="btn btn-xs btn-ghost btn-square"
                on:click={() => removeFromPlaylist(track)}>
                <X size="12" />
              </button>
            </div>
          </div>
        {/each}
      </div>
      
      <div class="modal-action">
        <button class="btn btn-sm btn-primary" on:click={() => showUploadMusic = true}>
          <Upload size="16" />
          Add Music
        </button>
        <button class="btn btn-sm" on:click={() => showMusicList = false}>
          Close
        </button>
      </div>
    </div>
  </div>
{/if}
```

### Component Properties
```typescript
interface MusicTrack {
  id: string;
  name: string;
  artist: string;
  duration: string;
  filePath: string;
  currentTime?: number;
}

interface SoundEffect {
  id: string;
  name: string;
  filePath: string;
  volume: number;
  isPlaying: boolean;
  loop: boolean;
}
```

---

## 7. Brand Controls (Always Visible)

### Purpose
Universal branding controls available across all tabs for consistent styling of comments, banners, and overlays.

### Visual Design
- Always-visible panel at bottom of controls
- Style selection grid
- Color picker interface
- Real-time preview updates

### Interaction Patterns
- **Style selection**: Choose from predefined brand styles
- **Color customization**: Primary and secondary color pickers
- **Real-time updates**: Immediate application to all branded elements
- **Frame options**: Border and styling options

### DaisyUI Implementation
```html
<div class="card bg-base-100 shadow-sm border-t-2 border-primary/20">
  <div class="card-header p-3 border-b border-base-200">
    <div class="flex items-center justify-between w-full">
      <h3 class="card-title text-sm font-semibold">Brand</h3>
      <div class="badge badge-primary badge-xs">Always Available</div>
    </div>
  </div>
  
  <div class="card-body p-3">
    <!-- Style Selection -->
    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text text-xs font-semibold">Style</span>
        <button class="btn btn-xs btn-ghost" on:click={resetToDefaults}>
          <RotateCcw size="12" />
          Reset
        </button>
      </label>
      <div class="grid grid-cols-2 gap-2">
        {#each brandStyles as style, index}
          <button 
            class="btn btn-sm relative"
            class:btn-primary={selectedStyle === index}
            class:btn-outline={selectedStyle !== index}
            on:click={() => setStyle(index)}>
            
            <span>Style {index + 1}</span>
            
            <!-- Style preview dots -->
            <div class="absolute bottom-1 right-1 flex gap-1">
              <div class="w-1 h-1 rounded-full bg-current opacity-60"></div>
              <div class="w-1 h-1 rounded-full bg-current opacity-40"></div>
              <div class="w-1 h-1 rounded-full bg-current opacity-20"></div>
            </div>
          </button>
        {/each}
      </div>
    </div>
    
    <!-- Color Customization -->
    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text text-xs font-semibold">Colors</span>
        <button class="btn btn-xs btn-ghost" on:click={openColorPresets}>
          <Palette size="12" />
        </button>
      </label>
      <div class="grid grid-cols-2 gap-3">
        <div class="form-control">
          <div class="relative">
            <input 
              type="color" 
              class="w-full h-10 rounded border border-base-300 cursor-pointer" 
              bind:value={brandColors.primary}
              on:change={updateBrandColors} />
            <div class="absolute inset-0 pointer-events-none border-2 border-base-300 rounded flex items-end p-1">
              <span class="text-xs font-medium text-white bg-black/50 px-1 rounded">Primary</span>
            </div>
          </div>
        </div>
        
        <div class="form-control">
          <div class="relative">
            <input 
              type="color" 
              class="w-full h-10 rounded border border-base-300 cursor-pointer" 
              bind:value={brandColors.secondary}
              on:change={updateBrandColors} />
            <div class="absolute inset-0 pointer-events-none border-2 border-base-300 rounded flex items-end p-1">
              <span class="text-xs font-medium text-white bg-black/50 px-1 rounded">Secondary</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Color harmony suggestions -->
      <div class="flex gap-1 mt-2">
        {#each colorHarmonies as harmony}
          <button 
            class="btn btn-xs btn-circle btn-outline"
            style="background: linear-gradient(45deg, {harmony.primary} 50%, {harmony.secondary} 50%)"
            on:click={() => applyColorHarmony(harmony)}
            title="{harmony.name}">
          </button>
        {/each}
      </div>
    </div>

    <!-- Frame and Effects -->
    <div class="grid grid-cols-2 gap-3">
      <div class="form-control">
        <label class="label">
          <span class="label-text text-xs">Frame</span>
        </label>
        <select class="select select-xs" bind:value={brandSettings.frameStyle}>
          <option value="none">None</option>
          <option value="simple">Simple</option>
          <option value="rounded">Rounded</option>
          <option value="sharp">Sharp</option>
          <option value="glow">Glow</option>
        </select>
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text text-xs">Opacity</span>
        </label>
        <input 
          type="range" 
          class="range range-xs" 
          min="50" 
          max="100" 
          bind:value={brandSettings.opacity} />
      </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="flex gap-2 mt-4">
      <button class="btn btn-xs btn-outline flex-1" on:click={savePreset}>
        <Save size="12" />
        Save Preset
      </button>
      <button class="btn btn-xs btn-outline flex-1" on:click={loadPreset}>
        <Folder size="12" />
        Load Preset
      </button>
    </div>
    
    <!-- Live Preview -->
    <div class="mt-4 p-3 bg-base-200 rounded">
      <p class="text-xs font-semibold mb-2">Live Preview</p>
      <div class="space-y-2">
        <!-- Comment preview -->
        <div 
          class="inline-block px-2 py-1 rounded text-xs"
          style="background-color: {brandColors.primary}; color: {brandColors.secondary}">
          Sample Comment
        </div>
        
        <!-- Banner preview -->
        <div 
          class="w-full p-2 text-center text-xs rounded"
          style="background-color: {brandColors.secondary}; color: {brandColors.primary}">
          Sample Banner Text
        </div>
      </div>
    </div>
  </div>
</div>
```

### Component Properties
```typescript
interface BrandSettings {
  selectedStyle: number;
  colors: {
    primary: string;
    secondary: string;
  };
  frameStyle: 'none' | 'simple' | 'rounded' | 'sharp' | 'glow';
  opacity: number;
}

interface ColorHarmony {
  name: string;
  primary: string;
  secondary: string;
}
```

---

## Implementation Integration

### State Management Structure
```typescript
// stores/studioEssentials.svelte.ts
export const studioEssentialsStore = {
  videos: $state<VideoClip[]>([]),
  overlays: $state<Overlay[]>([]),
  backgrounds: $state<Background[]>([]),
  comments: $state<Comment[]>([]),
  banners: $state<Banner[]>([]),
  musicTracks: $state<MusicTrack[]>([]),
  soundEffects: $state<SoundEffect[]>([]),
  brandSettings: $state<BrandSettings>(defaultBrandSettings),
  
  // Actions
  playVideo: (video: VideoClip) => { /* implementation */ },
  toggleOverlay: (overlay: Overlay) => { /* implementation */ },
  setBackground: (background: Background) => { /* implementation */ },
  createBanner: (banner: Partial<Banner>) => { /* implementation */ },
  toggleMusic: () => { /* implementation */ },
  playSoundEffect: (sfx: SoundEffect) => { /* implementation */ },
  updateBrandSettings: (settings: Partial<BrandSettings>) => { /* implementation */ },
};
```

### Component Integration Pattern
Each Studio Essential component should:
1. Import the shared store
2. Use reactive statements for real-time updates
3. Emit events for Preview component integration
4. Follow DaisyUI theming conventions
5. Support keyboard shortcuts for power users

This documentation provides the foundation for implementing the core Studio Essentials that will give your Evmux clone professional-grade streaming capabilities. Each component is designed to work independently while sharing common styling and state management patterns.