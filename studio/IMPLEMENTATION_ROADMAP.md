# Studio Essentials Implementation Roadmap
*Step-by-step guide to implementing Evmux clone features*

## Overview
This roadmap provides a practical implementation plan for the Studio Essentials documented in `STUDIO_ESSENTIALS_UI_DOCUMENTATION.md`. It focuses on integrating these features into your existing SvelteKit application with proper state management, component architecture, and DaisyUI styling.

---

## Phase 1: Foundation Setup (Week 1)

### 1.1 State Management Architecture
Create the core state management system for Studio Essentials:

**File: `studio/src/lib/stores/studioEssentials.svelte.ts`**
```typescript
import { writable, derived } from 'svelte/store';

// Types
export interface VideoClip {
  id: string;
  name: string;
  thumbnail: string;
  duration: string;
  filePath: string;
  isPlaying: boolean;
}

export interface Overlay {
  id: string;
  name: string;
  thumbnail: string;
  type: 'transparent' | 'fullscreen' | 'layout';
  filePath: string;
  isActive: boolean;
  layoutCompatibility: string[];
}

export interface Background {
  id: string;
  name: string;
  thumbnail: string;
  type: 'image' | 'video';
  filePath: string;
  isActive: boolean;
}

export interface Comment {
  id: string;
  author: string;
  message: string;
  timestamp: string;
  platform: 'youtube' | 'facebook' | 'twitch' | 'linkedin';
  avatar?: string;
}

export interface Banner {
  id: string;
  primaryText: string;
  secondaryText?: string;
  position: 'top' | 'bottom' | 'middle';
  isTicker: boolean;
  isActive: boolean;
  style: BannerStyle;
}

export interface BannerStyle {
  backgroundColor: string;
  textColor: string;
  fontSize: string;
  borderRadius: string;
}

export interface MusicTrack {
  id: string;
  name: string;
  artist: string;
  duration: string;
  filePath: string;
  currentTime?: number;
}

export interface SoundEffect {
  id: string;
  name: string;
  filePath: string;
  volume: number;
  isPlaying: boolean;
  loop: boolean;
}

export interface BrandSettings {
  selectedStyle: number;
  colors: {
    primary: string;
    secondary: string;
  };
  frameStyle: 'none' | 'simple' | 'rounded' | 'sharp' | 'glow';
  opacity: number;
}

// State
class StudioEssentialsState {
  videos = $state<VideoClip[]>([]);
  overlays = $state<Overlay[]>([]);
  backgrounds = $state<Background[]>([]);
  comments = $state<Comment[]>([]);
  banners = $state<Banner[]>([]);
  musicTracks = $state<MusicTrack[]>([]);
  soundEffects = $state<SoundEffect[]>([]);
  brandSettings = $state<BrandSettings>({
    selectedStyle: 0,
    colors: { primary: '#3b82f6', secondary: '#1f2937' },
    frameStyle: 'none',
    opacity: 100
  });
  
  // Music state
  musicPlaying = $state(false);
  musicVolume = $state(50);
  sfxVolume = $state(75);
  currentTrack = $state<MusicTrack | null>(null);
  
  // Active content tracking
  activeOverlays = $derived(() => this.overlays.filter(o => o.isActive));
  activeBackground = $derived(() => this.backgrounds.find(b => b.isActive));
  activeBanners = $derived(() => this.banners.filter(b => b.isActive));
  
  // Actions
  playVideo = (video: VideoClip) => {
    // Stop any currently playing video
    this.videos.forEach(v => v.isPlaying = false);
    
    // Play the selected video
    video.isPlaying = true;
    
    // Emit event for preview component
    this.emitPreviewUpdate('video_play', video);
    
    // Auto-stop after duration (mock implementation)
    setTimeout(() => {
      video.isPlaying = false;
      this.emitPreviewUpdate('video_stop', video);
    }, this.parseDuration(video.duration));
  };
  
  toggleOverlay = (overlay: Overlay) => {
    overlay.isActive = !overlay.isActive;
    this.emitPreviewUpdate('overlay_toggle', overlay);
  };
  
  setBackground = (background: Background) => {
    // Deactivate all backgrounds
    this.backgrounds.forEach(b => b.isActive = false);
    
    // Activate selected background
    background.isActive = true;
    this.emitPreviewUpdate('background_change', background);
  };
  
  createBanner = (bannerData: Partial<Banner>) => {
    const newBanner: Banner = {
      id: crypto.randomUUID(),
      primaryText: bannerData.primaryText || '',
      secondaryText: bannerData.secondaryText,
      position: bannerData.position || 'bottom',
      isTicker: bannerData.isTicker || false,
      isActive: false,
      style: {
        backgroundColor: this.brandSettings.colors.primary,
        textColor: this.brandSettings.colors.secondary,
        fontSize: '14px',
        borderRadius: '4px'
      }
    };
    
    this.banners.push(newBanner);
    return newBanner;
  };
  
  toggleBanner = (banner: Banner) => {
    banner.isActive = !banner.isActive;
    this.emitPreviewUpdate('banner_toggle', banner);
  };
  
  toggleMusic = () => {
    this.musicPlaying = !this.musicPlaying;
    this.emitPreviewUpdate('music_toggle', { playing: this.musicPlaying });
  };
  
  playSoundEffect = (sfx: SoundEffect) => {
    if (sfx.isPlaying) return;
    
    sfx.isPlaying = true;
    this.emitPreviewUpdate('sfx_play', sfx);
    
    // Mock playback duration
    setTimeout(() => {
      sfx.isPlaying = false;
    }, 2000);
  };
  
  updateBrandSettings = (settings: Partial<BrandSettings>) => {
    Object.assign(this.brandSettings, settings);
    this.emitPreviewUpdate('brand_update', this.brandSettings);
  };
  
  // Helper methods
  private parseDuration = (duration: string): number => {
    // Parse duration like "0:30" to milliseconds
    const [minutes, seconds] = duration.split(':').map(Number);
    return (minutes * 60 + seconds) * 1000;
  };
  
  private emitPreviewUpdate = (type: string, data: any) => {
    // Dispatch custom event for preview component
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('studio-update', {
        detail: { type, data }
      }));
    }
  };
  
  // Initialization
  loadDefaultAssets = async () => {
    // Load default overlays, backgrounds, etc.
    this.overlays = [
      {
        id: '1',
        name: 'Lower Third',
        thumbnail: '/assets/overlays/lower-third-thumb.png',
        type: 'transparent',
        filePath: '/assets/overlays/lower-third.png',
        isActive: false,
        layoutCompatibility: ['single', 'dual']
      },
      {
        id: '2',
        name: 'Full Screen Logo',
        thumbnail: '/assets/overlays/fullscreen-logo-thumb.png',
        type: 'fullscreen',
        filePath: '/assets/overlays/fullscreen-logo.png',
        isActive: false,
        layoutCompatibility: ['all']
      }
    ];
    
    this.backgrounds = [
      {
        id: '1',
        name: 'Studio Background',
        thumbnail: '/assets/backgrounds/studio-thumb.jpg',
        type: 'image',
        filePath: '/assets/backgrounds/studio.jpg',
        isActive: true
      }
    ];
    
    this.soundEffects = [
      {
        id: '1',
        name: 'Air Horn',
        filePath: '/assets/sfx/airhorn.mp3',
        volume: 80,
        isPlaying: false,
        loop: false
      },
      {
        id: '2',
        name: 'Applause',
        filePath: '/assets/sfx/applause.mp3',
        volume: 70,
        isPlaying: false,
        loop: false
      }
    ];
  };
}

export const studioEssentialsStore = new StudioEssentialsState();
```

### 1.2 Component Structure Setup
Organize the new components in your existing structure:

```
studio/src/lib/components/
├── Controls.svelte (existing - will be updated)
├── studio-essentials/
│   ├── VideoClipsPanel.svelte
│   ├── OverlaysPanel.svelte
│   ├── BackgroundsPanel.svelte
│   ├── CommentsPanel.svelte
│   ├── BannersPanel.svelte
│   ├── MusicPanel.svelte
│   └── BrandControlsPanel.svelte
└── modals/
    ├── BannerCreateModal.svelte
    ├── MusicPlaylistModal.svelte
    └── AssetUploadModal.svelte
```

### 1.3 Update Controls.svelte
Modify your existing Controls component to include the new panels:

**File: `studio/src/lib/components/Controls.svelte`**
```svelte
<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import VideoClipsPanel from './studio-essentials/VideoClipsPanel.svelte';
  import OverlaysPanel from './studio-essentials/OverlaysPanel.svelte';
  import BackgroundsPanel from './studio-essentials/BackgroundsPanel.svelte';
  import CommentsPanel from './studio-essentials/CommentsPanel.svelte';
  import BannersPanel from './studio-essentials/BannersPanel.svelte';
  import MusicPanel from './studio-essentials/MusicPanel.svelte';
  import BrandControlsPanel from './studio-essentials/BrandControlsPanel.svelte';
  import { onMount } from 'svelte';
  
  let activeTab = $state('videos');
  
  onMount(() => {
    studioEssentialsStore.loadDefaultAssets();
  });
</script>

<div class="w-80 flex flex-col bg-base-100 shadow-sm rounded-lg overflow-hidden">
  <!-- Tab Navigation -->
  <div class="tabs tabs-bordered bg-base-200">
    <button 
      class="tab tab-sm"
      class:tab-active={activeTab === 'videos'}
      on:click={() => activeTab = 'videos'}>
      Videos
    </button>
    <button 
      class="tab tab-sm"
      class:tab-active={activeTab === 'overlays'}
      on:click={() => activeTab = 'overlays'}>
      Overlays
    </button>
    <button 
      class="tab tab-sm"
      class:tab-active={activeTab === 'backgrounds'}
      on:click={() => activeTab = 'backgrounds'}>
      Backgrounds
    </button>
    <button 
      class="tab tab-sm"
      class:tab-active={activeTab === 'comments'}
      on:click={() => activeTab = 'comments'}>
      Comments
    </button>
    <button 
      class="tab tab-sm"
      class:tab-active={activeTab === 'banners'}
      on:click={() => activeTab = 'banners'}>
      Banners
    </button>
    <button 
      class="tab tab-sm"
      class:tab-active={activeTab === 'music'}
      on:click={() => activeTab = 'music'}>
      Music
    </button>
  </div>
  
  <!-- Tab Content -->
  <div class="flex-1 overflow-y-auto">
    {#if activeTab === 'videos'}
      <VideoClipsPanel />
    {:else if activeTab === 'overlays'}
      <OverlaysPanel />
    {:else if activeTab === 'backgrounds'}
      <BackgroundsPanel />
    {:else if activeTab === 'comments'}
      <CommentsPanel />
    {:else if activeTab === 'banners'}
      <BannersPanel />
    {:else if activeTab === 'music'}
      <MusicPanel />
    {/if}
  </div>
  
  <!-- Always-visible Brand Controls -->
  <BrandControlsPanel />
</div>
```

---

## Phase 2: Core Components (Week 2-3)

### 2.1 Video Clips Panel
**File: `studio/src/lib/components/studio-essentials/VideoClipsPanel.svelte`**
```svelte
<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Plus, Play, Video } from 'lucide-svelte';
  
  let { videos } = $derived(studioEssentialsStore);
  let showUpload = $state(false);
  
  function handleVideoClick(video: any) {
    studioEssentialsStore.playVideo(video);
  }
  
  function handleVideoUpload() {
    showUpload = true;
    // Implementation for file upload
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Video Clips</h3>
    <button 
      class="btn btn-sm btn-ghost btn-square tooltip" 
      data-tip="Add video clip"
      on:click={handleVideoUpload}>
      <Plus size="16" />
    </button>
  </div>
  
  <!-- Video Grid -->
  <div class="space-y-2">
    {#each videos as video}
      <div class="relative group">
        <div 
          class="aspect-video bg-base-200 rounded overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all"
          on:click={() => handleVideoClick(video)}>
          
          <img 
            src={video.thumbnail} 
            alt={video.name} 
            class="w-full h-full object-cover" />
          
          {#if video.isPlaying}
            <div class="absolute top-2 right-2 flex items-center gap-1">
              <div class="w-2 h-2 bg-error rounded-full animate-pulse"></div>
              <span class="text-xs bg-error text-error-content px-1 rounded">LIVE</span>
            </div>
          {/if}
          
          <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button class="btn btn-circle btn-primary btn-sm">
              <Play size="16" />
            </button>
          </div>
        </div>
        
        <p class="text-xs mt-1 font-medium truncate">{video.name}</p>
        <p class="text-xs text-base-content/60">{video.duration}</p>
      </div>
    {/each}
    
    {#if videos.length === 0}
      <div class="text-center py-8">
        <Video size="32" class="mx-auto text-base-content/40 mb-2" />
        <p class="text-xs text-base-content/60 mb-2">No video clips yet</p>
        <button class="btn btn-sm btn-primary" on:click={handleVideoUpload}>
          Add Video
        </button>
      </div>
    {/if}
  </div>
</div>
```

### 2.2 Overlays Panel
**File: `studio/src/lib/components/studio-essentials/OverlaysPanel.svelte`**
```svelte
<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Plus } from 'lucide-svelte';
  
  let { overlays } = $derived(studioEssentialsStore);
  let activeCategory = $state('all');
  
  $: filteredOverlays = overlays.filter(overlay => 
    activeCategory === 'all' || overlay.type === activeCategory
  );
  
  function toggleOverlay(overlay: any) {
    studioEssentialsStore.toggleOverlay(overlay);
  }
</script>

<div class="p-3 space-y-3">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h3 class="font-semibold text-sm">Overlays</h3>
    <button class="btn btn-sm btn-ghost btn-square tooltip" data-tip="Add overlay">
      <Plus size="16" />
    </button>
  </div>
  
  <!-- Category Tabs -->
  <div class="tabs tabs-boxed tabs-xs">
    <button 
      class="tab"
      class:tab-active={activeCategory === 'all'}
      on:click={() => activeCategory = 'all'}>
      All
    </button>
    <button 
      class="tab"
      class:tab-active={activeCategory === 'transparent'}
      on:click={() => activeCategory = 'transparent'}>
      Transparent
    </button>
    <button 
      class="tab"
      class:tab-active={activeCategory === 'fullscreen'}
      on:click={() => activeCategory = 'fullscreen'}>
      Full Screen
    </button>
  </div>
  
  <!-- Overlay Grid -->
  <div class="grid grid-cols-2 gap-2">
    {#each filteredOverlays as overlay}
      <div class="relative">
        <button 
          class="aspect-video w-full bg-base-200 rounded overflow-hidden border-2 transition-all hover:scale-105"
          class:border-primary={overlay.isActive}
          class:border-base-300={!overlay.isActive}
          class:shadow-lg={overlay.isActive}
          on:click={() => toggleOverlay(overlay)}>
          
          <img 
            src={overlay.thumbnail} 
            alt={overlay.name} 
            class="w-full h-full object-cover" />
          
          {#if overlay.isActive}
            <div class="absolute inset-0 bg-primary/20 flex items-center justify-center">
              <div class="badge badge-primary badge-sm font-bold">ON</div>
            </div>
          {/if}
          
          <div class="absolute top-1 left-1">
            <div class="badge badge-xs badge-outline bg-base-100/80">
              {overlay.type}
            </div>
          </div>
        </button>
        
        <p class="text-xs mt-1 truncate text-center font-medium">{overlay.name}</p>
      </div>
    {/each}
  </div>
</div>
```

### 2.3 Brand Controls Panel
**File: `studio/src/lib/components/studio-essentials/BrandControlsPanel.svelte`**
```svelte
<script lang="ts">
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  import { Palette, RotateCcw, Save, Folder } from 'lucide-svelte';
  
  let { brandSettings } = $derived(studioEssentialsStore);
  
  const brandStyles = [
    { name: 'Style 1', preview: 'Modern' },
    { name: 'Style 2', preview: 'Classic' },
    { name: 'Style 3', preview: 'Bold' },
    { name: 'Style 4', preview: 'Minimal' }
  ];
  
  function setStyle(index: number) {
    studioEssentialsStore.updateBrandSettings({ selectedStyle: index });
  }
  
  function updateColors() {
    studioEssentialsStore.updateBrandSettings({ colors: brandSettings.colors });
  }
</script>

<div class="border-t-2 border-primary/20 bg-base-100">
  <div class="p-3">
    <div class="flex items-center justify-between mb-3">
      <h3 class="font-semibold text-sm">Brand</h3>
      <div class="badge badge-primary badge-xs">Always Available</div>
    </div>
    
    <!-- Style Selection -->
    <div class="mb-3">
      <label class="label">
        <span class="label-text text-xs font-semibold">Style</span>
        <button class="btn btn-xs btn-ghost">
          <RotateCcw size="12" />
        </button>
      </label>
      <div class="grid grid-cols-2 gap-2">
        {#each brandStyles as style, index}
          <button 
            class="btn btn-sm relative"
            class:btn-primary={brandSettings.selectedStyle === index}
            class:btn-outline={brandSettings.selectedStyle !== index}
            on:click={() => setStyle(index)}>
            <span>Style {index + 1}</span>
          </button>
        {/each}
      </div>
    </div>
    
    <!-- Color Customization -->
    <div class="mb-3">
      <label class="label">
        <span class="label-text text-xs font-semibold">Colors</span>
        <button class="btn btn-xs btn-ghost">
          <Palette size="12" />
        </button>
      </label>
      <div class="grid grid-cols-2 gap-3">
        <div class="form-control">
          <div class="relative">
            <input 
              type="color" 
              class="w-full h-10 rounded border border-base-300 cursor-pointer" 
              bind:value={brandSettings.colors.primary}
              on:change={updateColors} />
            <div class="absolute inset-0 pointer-events-none border-2 border-base-300 rounded flex items-end p-1">
              <span class="text-xs font-medium text-white bg-black/50 px-1 rounded">Primary</span>
            </div>
          </div>
        </div>
        
        <div class="form-control">
          <div class="relative">
            <input 
              type="color" 
              class="w-full h-10 rounded border border-base-300 cursor-pointer" 
              bind:value={brandSettings.colors.secondary}
              on:change={updateColors} />
            <div class="absolute inset-0 pointer-events-none border-2 border-base-300 rounded flex items-end p-1">
              <span class="text-xs font-medium text-white bg-black/50 px-1 rounded">Secondary</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Live Preview -->
    <div class="mt-3 p-2 bg-base-200 rounded">
      <p class="text-xs font-semibold mb-2">Live Preview</p>
      <div class="space-y-1">
        <div 
          class="inline-block px-2 py-1 rounded text-xs"
          style="background-color: {brandSettings.colors.primary}; color: {brandSettings.colors.secondary}">
          Sample Comment
        </div>
        <div 
          class="w-full p-1 text-center text-xs rounded"
          style="background-color: {brandSettings.colors.secondary}; color: {brandSettings.colors.primary}">
          Sample Banner
        </div>
      </div>
    </div>
  </div>
</div>
```

---

## Phase 3: Advanced Components (Week 4-5)

### 3.1 Comments Panel with Real-time Styling
### 3.2 Banner Creation with Modal
### 3.3 Music & SFX Management
### 3.4 Background Management with Upload

---

## Phase 4: Integration & Preview Updates (Week 6)

### 4.1 Update Preview Component
Modify your existing Preview component to respond to studio essential changes:

**File: `studio/src/lib/components/Preview.svelte`** (additions)
```svelte
<script lang="ts">
  import { onMount } from 'svelte';
  import { studioEssentialsStore } from '$lib/stores/studioEssentials.svelte';
  
  let previewElement: HTMLDivElement;
  let { activeOverlays, activeBackground, activeBanners } = $derived(studioEssentialsStore);
  
  onMount(() => {
    // Listen for studio updates
    window.addEventListener('studio-update', handleStudioUpdate);
    
    return () => {
      window.removeEventListener('studio-update', handleStudioUpdate);
    };
  });
  
  function handleStudioUpdate(event: CustomEvent) {
    const { type, data } = event.detail;
    
    switch (type) {
      case 'video_play':
        playVideoInPreview(data);
        break;
      case 'overlay_toggle':
        updateOverlayDisplay(data);
        break;
      case 'background_change':
        updateBackgroundDisplay(data);
        break;
      case 'banner_toggle':
        updateBannerDisplay(data);
        break;
      case 'brand_update':
        updateBrandStyling(data);
        break;
    }
  }
  
  function playVideoInPreview(video: any) {
    // Implementation for video playback in preview
  }
  
  function updateOverlayDisplay(overlay: any) {
    // Implementation for overlay display
  }
  
  function updateBackgroundDisplay(background: any) {
    // Implementation for background display
  }
  
  function updateBannerDisplay(banner: any) {
    // Implementation for banner display
  }
  
  function updateBrandStyling(brandSettings: any) {
    // Implementation for brand styling updates
  }
</script>

<div bind:this={previewElement} class="preview-container relative">
  <!-- Existing preview content -->
  
  <!-- Active Background -->
  {#if activeBackground}
    <div class="absolute inset-0 -z-10">
      <img 
        src={activeBackground.filePath} 
        alt={activeBackground.name}
        class="w-full h-full object-cover" />
    </div>
  {/if}
  
  <!-- Active Overlays -->
  {#each activeOverlays as overlay}
    <div class="absolute inset-0 pointer-events-none">
      <img 
        src={overlay.filePath} 
        alt={overlay.name}
        class="w-full h-full object-contain" />
    </div>
  {/each}
  
  <!-- Active Banners -->
  {#each activeBanners as banner}
    <div 
      class="absolute w-full px-4 py-2 text-center z-10"
      class:top-0={banner.position === 'top'}
      class:bottom-0={banner.position === 'bottom'}
      class:top-1/2={banner.position === 'middle'}
      class:-translate-y-1/2={banner.position === 'middle'}
      style="background-color: {banner.style.backgroundColor}; color: {banner.style.textColor}">
      
      <div class:animate-marquee={banner.isTicker}>
        <span class="font-semibold">{banner.primaryText}</span>
        {#if banner.secondaryText}
          <span class="ml-2 opacity-80">{banner.secondaryText}</span>
        {/if}
      </div>
    </div>
  {/each}
</div>

<style>
  @keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
  }
  
  .animate-marquee {
    animation: marquee 15s linear infinite;
  }
</style>
```

---

## Phase 5: File Upload & Asset Management (Week 7)

### 5.1 Asset Upload Service
### 5.2 File Processing & Thumbnail Generation
### 5.3 Asset Storage Management

---

## Phase 6: Testing & Polish (Week 8)

### 6.1 Component Testing
### 6.2 Integration Testing
### 6.3 Performance Optimization
### 6.4 Accessibility Improvements

---

## Implementation Notes

### DaisyUI Integration
- All components use semantic color names (`primary`, `secondary`, `base-100`, etc.)
- Responsive design with `sm:`, `md:`, `lg:` prefixes
- Component modifiers follow daisyUI conventions
- Consistent spacing with Tailwind classes

### State Management
- Use Svelte 5's `$state` and `$derived` for reactive state
- Event-driven communication between components
- Centralized store for all studio essentials
- Real-time updates through custom events

### File Structure
```
studio/src/lib/
├── stores/
│   └── studioEssentials.svelte.ts
├── components/
│   ├── studio-essentials/
│   │   ├── VideoClipsPanel.svelte
│   │   ├── OverlaysPanel.svelte
│   │   ├── BackgroundsPanel.svelte
│   │   ├── CommentsPanel.svelte
│   │   ├── BannersPanel.svelte
│   │   ├── MusicPanel.svelte
│   │   └── BrandControlsPanel.svelte
│   └── modals/
│       ├── BannerCreateModal.svelte
│       ├── MusicPlaylistModal.svelte
│       └── AssetUploadModal.svelte
├── services/
│   ├── assetUpload.ts
│   ├── audioProcessor.ts
│   └── thumbnailGenerator.ts
└── types/
    └── studioEssentials.ts
```

### Performance Considerations
- Lazy load video thumbnails
- Optimize overlay rendering
- Debounce brand color updates
- Use virtual scrolling for large asset lists
- Implement asset caching

This roadmap provides a structured approach to implementing the Studio Essentials while maintaining clean code architecture and following SvelteKit best practices.