# Edify Studio - Professional Streaming Platform

A modern streaming studio application built with SvelteKit and integrated with Media over QUIC (MoQ) for real-time audio/video publishing and guest communication.

## 🚀 Features

- **Real-time Streaming**: Direct integration with `@kixelated/hang` MoQ library
- **Multi-participant Support**: Host and guest streaming capabilities
- **Professional UI**: Clean, modern interface built with DaisyUI
- **Quality Control**: 4K/HD/SD streaming options
- **Device Management**: Camera and screen sharing support
- **Guest Invitations**: Email-based guest invitation system
- **Reactive State**: Svelte 5 runes for optimal performance

## 🏗️ Architecture

### Core Components

1. **MoQ Services** (`src/lib/services/`)
   - `StudioPublisher`: Direct MoQ publishing for host streams
   - `StudioWatcher`: MoQ consumption for guest streams

2. **Streaming Store** (`src/lib/stores/streaming.svelte.ts`)
   - Centralized state management
   - Bridges MoQ signals with Svelte reactivity
   - Participant and stream management

3. **UI Components** (`src/lib/components/`)
   - `Header`: Studio branding and stream controls
   - `Preview`: Real-time video preview with overlays
   - `Sidebar`: Scene and layout management
   - `Controls`: Layer and audio controls
   - `Footer`: Participant management and invitations

4. **Configuration** (`src/lib/config/streaming.ts`)
   - Centralized settings for relay URLs, quality options, etc.

### MoQ Integration

The application bypasses Web Components for direct TypeScript class control:

```typescript
// Direct class usage instead of <hang-publish> web component
const publisher = new StudioPublisher({
  relayUrl: 'http://localhost:4443',
  streamPath: 'studio/host',
  device: 'camera'
});

await publisher.startStream();
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- Bun (recommended) or npm
- MoQ relay server running on localhost:4443

### Installation

```bash
# Install dependencies
bun install

# Copy environment configuration
cp .env.example .env

# Start development server
bun run dev
```

### Environment Configuration

```env
# MoQ Relay Server
VITE_MOQ_RELAY_URL=http://localhost:4443

# Studio Branding
VITE_STUDIO_NAME="Edify Studio"
VITE_STUDIO_LOGO="E"

# Development Options
VITE_DEBUG_MODE=true
```

## 📡 MoQ Relay Server

The application requires a MoQ relay server. For development:

```bash
# In the root project directory
cargo run --bin relay
```

The relay server will start on `http://localhost:4443` by default.

## 🎯 Usage

### Starting a Stream

1. **Initialize**: The app automatically initializes the MoQ publisher on load
2. **Connect**: Connection status is shown in the top status bar
3. **Start Stream**: Click "Start Stream" button to begin broadcasting
4. **Quality Control**: Use the quality dropdown to adjust stream resolution

### Inviting Guests

1. **Invite Guest**: Click "Invite Guest" in the footer
2. **Enter Email**: Provide guest email address
3. **Send Invitation**: Guest receives unique stream path
4. **Join Stream**: Guest uses provided link to join studio

### Stream Management

- **Audio/Video Toggle**: Use preview controls to mute/unmute
- **Quality Settings**: Real-time quality adjustment
- **Participant Switching**: Click participant thumbnails to switch focus
- **Device Selection**: Camera or screen sharing options

## 🔧 Technical Implementation

### State Management Pattern

```typescript
// Reactive state with Svelte 5 runes
let isStreaming = $derived(streamingStore.isStreaming);
let connectionStatus = $derived(streamingStore.connectionStatus);

// Direct MoQ integration
streamingStore.onStatusChange((status) => {
  // Handle connection and streaming status changes
});
```

### Multi-Stream Architecture

- **Host Publisher**: Single `StudioPublisher` instance for main stream
- **Guest Watchers**: Multiple `StudioWatcher` instances for guest streams
- **Stream Coordination**: Centralized management via streaming store

### Error Handling

- Connection retry logic with exponential backoff
- WebTransport compatibility detection
- Graceful fallbacks for unsupported browsers

## 🚀 Production Deployment

### Environment Setup

```env
# Production relay server
VITE_MOQ_RELAY_URL=https://relay.your-domain.com

# Disable debug features
VITE_DEBUG_MODE=false
```

### Build Process

```bash
# Build for production
bun run build

# Preview production build
bun run preview
```

### Server Requirements

- HTTPS required for WebTransport
- Valid SSL certificate
- MoQ relay server with proper CORS configuration

## 🔍 Troubleshooting

### Common Issues

1. **WebTransport Not Supported**
   - Requires modern browser (Chrome 97+, Firefox 114+)
   - HTTPS required in production

2. **Connection Failed**
   - Verify MoQ relay server is running
   - Check CORS configuration
   - Validate relay URL in environment

3. **No Video Preview**
   - Check camera permissions
   - Verify device access in browser settings
   - Ensure getUserMedia is available

### Debug Mode

Enable debug logging:

```env
VITE_DEBUG_MODE=true
```

This enables console logging for:
- MoQ connection status
- Stream publishing events
- Participant management actions

## 📦 Dependencies

- **@kixelated/hang**: MoQ streaming library
- **SvelteKit**: Application framework
- **DaisyUI**: UI component system
- **Lucide Svelte**: Icon library
- **TypeScript**: Type safety

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

## 📄 License

[Add your license information here]
