# 🧪 Edify API Testing Guide

Comprehensive shell scripts for testing the Edify RPC API using curl commands.

## 📋 Available Scripts

### 1. `./test_edify_api.sh` - Comprehensive Testing

**Purpose**: Full API test suite covering all endpoints with detailed validation
**Duration**: ~2-3 minutes
**Coverage**: 40+ test cases

**Features**:
- ✅ Complete endpoint coverage (System, Load Balancer, Hub, Relay)
- ✅ CRUD operations testing (Sources, Destinations, Routes)
- ✅ Error condition testing
- ✅ Response validation with jq
- ✅ Detailed logging and reporting
- ✅ Automatic cleanup of test resources

**Usage**:
```bash
# Test against default server (localhost:4443)
./test_edify_api.sh

# Test against custom server
./test_edify_api.sh http://your-server:8080
```

### 2. `./quick_api_test.sh` - Basic Health Check

**Purpose**: Quick connectivity and basic functionality test
**Duration**: ~10 seconds
**Coverage**: 9 essential endpoints

**Features**:
- ✅ Fast health check of all major components
- ✅ Simple pass/fail results
- ✅ Minimal output for CI/CD pipelines
- ✅ No resource creation or cleanup needed

**Usage**:
```bash
# Quick test against default server
./quick_api_test.sh

# Quick test against custom server
./quick_api_test.sh http://your-server:8080
```

## 🎯 When to Use Each Script

### Use `quick_api_test.sh` when:
- ✅ Verifying server is running and responsive
- ✅ CI/CD pipeline health checks
- ✅ Quick development environment validation
- ✅ Before running more intensive tests

### Use `test_edify_api.sh` when:
- ✅ Comprehensive API validation
- ✅ Testing new features or bug fixes
- ✅ End-to-end functionality verification
- ✅ Documentation validation
- ✅ Release testing

## 📊 Test Coverage

### System Endpoints
```bash
GET  /health          # Server health and hub connectivity
GET  /version          # Version and build information
```

### Load Balancer Endpoints
```bash
GET  /balancer/status       # Load balancer status
GET  /balancer/nodes        # Available nodes list
GET  /balancer/recommend    # Simple recommendation
POST /balancer/recommend    # Detailed recommendation with requirements
```

### Stream Hub Endpoints
```bash
GET    /hub/status                              # Hub statistics
GET    /hub/sources                             # List sources
POST   /hub/sources                             # Add source
DELETE /hub/sources/{id}                        # Remove source
POST   /hub/sources/{id}/start                  # Start source
POST   /hub/sources/{id}/stop                   # Stop source
GET    /hub/destinations                        # List destinations
POST   /hub/destinations                        # Add destination
DELETE /hub/destinations/{id}                   # Remove destination
POST   /hub/destinations/{id}/start             # Start destination
POST   /hub/destinations/{id}/stop              # Stop destination
GET    /hub/routes                              # List routes
POST   /hub/routes                              # Create route
DELETE /hub/routes/{source_id}/{destination_id} # Remove route
```

### Relay Endpoints (MoQ)
```bash
GET /relay/certificate.sha256    # TLS certificate fingerprint
GET /relay/announced             # Announced tracks (root)
GET /relay/announced/{*prefix}   # Announced tracks (prefix)
GET /relay/fetch/{*path}         # Fetch specific track
```

## 🔧 Prerequisites

### Required Tools
```bash
# Install required dependencies
sudo apt-get update
sudo apt-get install curl jq

# Or on macOS
brew install curl jq
```

### Server Requirements
- Edify server running on target URL
- Network connectivity to server
- Server configured with proper CORS headers

## 📝 Example Usage Scenarios

### 1. Development Workflow
```bash
# Start Edify server
cargo run --bin edify

# In another terminal - Quick health check
./quick_api_test.sh

# Run full test suite
./test_edify_api.sh
```

### 2. CI/CD Pipeline
```bash
# In your CI script
if ./quick_api_test.sh; then
    echo "✅ Server is healthy, proceeding with deployment"
    ./test_edify_api.sh
else
    echo "❌ Server health check failed"
    exit 1
fi
```

### 3. Custom Server Testing
```bash
# Test staging environment
./test_edify_api.sh https://edify-staging.example.com

# Test local development with custom port
./quick_api_test.sh http://localhost:8080
```

## 📋 Test Data Examples

### Source Configurations
```json
// RTMP Source (OBS, streaming software)
{
  "name": "Test RTMP Source",
  "input_url": "rtmp://localhost/live/test_stream",
  "auto_start": false
}

// MoQ Source (low-latency)
{
  "name": "Test MoQ Source", 
  "input_url": "moq://localhost:4443/live/test_moq",
  "auto_start": false
}

// File Source (testing)
{
  "name": "Test File Source",
  "input_url": "file:///tmp/test_video.mp4", 
  "auto_start": false
}
```

### Destination Configurations
```json
// RTMP Destination (YouTube, Twitch, etc.)
{
  "name": "Test RTMP Destination",
  "output_url": "rtmp://localhost/output/test_output",
  "auto_start": false
}

// YouTube Live
{
  "name": "Test YouTube Destination",
  "output_url": "rtmp://a.rtmp.youtube.com/live2/TEST_STREAM_KEY",
  "auto_start": false
}

// MoQ Destination (low-latency output)
{
  "name": "Test MoQ Destination",
  "output_url": "moq://localhost:4443/output/test_moq",
  "auto_start": false
}
```

### Load Balancer Recommendation
```json
{
  "stream_type": "live",
  "preferred_region": "us-east", 
  "requirements": {
    "min_bandwidth": 1000000,
    "max_latency": 100,
    "codec_preferences": ["h264", "hevc"]
  }
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Connection Refused
```bash
curl: (7) Failed to connect to localhost port 4443: Connection refused
```
**Solution**: Ensure Edify server is running on the specified port

#### 2. JSON Parse Errors
```bash
parse error: Invalid numeric literal at line 1, column 1
```
**Solution**: Server returned non-JSON response, check server logs

#### 3. Permission Denied
```bash
bash: ./test_edify_api.sh: Permission denied
```
**Solution**: Make script executable: `chmod +x test_edify_api.sh`

#### 4. Missing Dependencies
```bash
jq: command not found
```
**Solution**: Install jq: `sudo apt-get install jq` or `brew install jq`

### Debug Mode

Enable verbose curl output for debugging:
```bash
# Add -v flag to curl commands for verbose output
curl -v http://localhost:4443/health
```

### Log Analysis

Check test logs for detailed information:
```bash
# View comprehensive test logs
cat /tmp/edify_api_test/api_test.log

# View last test response
cat /tmp/edify_api_test/response.json | jq '.'
```

## 📈 Expected Results

### Successful Quick Test Output
```
🚀 Quick Edify API Test
Server: http://localhost:4443
========================
Testing Health Check... ✅
Testing Version Info... ✅
Testing Load Balancer... ✅
Testing Node List... ✅
Testing Hub Status... ✅
Testing Sources List... ✅
Testing Destinations List... ✅
Testing Routes List... ✅
Testing TLS Certificate... ✅

Results: 9/9 tests passed
🎉 All basic tests passed!
```

### Comprehensive Test Summary
```
🎯 TEST SUMMARY
===============
Total Tests: 45
Passed: 45
Failed: 0
🎉 ALL TESTS PASSED!

📄 Detailed logs saved to: /tmp/edify_api_test/api_test.log
🗂️ Test artifacts in: /tmp/edify_api_test
```

## 🔄 Integration with Development

### Pre-commit Hook
Add to `.git/hooks/pre-commit`:
```bash
#!/bin/bash
echo "Running API tests..."
if ! ./quick_api_test.sh; then
    echo "❌ API tests failed, commit rejected"
    exit 1
fi
```

### Make Targets
Add to `Makefile`:
```makefile
test-api-quick:
	./quick_api_test.sh

test-api-full:
	./test_edify_api.sh

test-api: test-api-quick test-api-full
```

### GitHub Actions
```yaml
- name: Test API
  run: |
    cargo run --bin edify &
    sleep 5
    ./quick_api_test.sh
    ./test_edify_api.sh
```

## 📚 Additional Resources

- [Edify RPC API Documentation](./edify/src/rpc/)
- [EXPERIMENTAL_GUIDE.md](./EXPERIMENTAL_GUIDE.md) - Complete API reference
- [studio/README_EXPERIMENTAL.md](./studio/README_EXPERIMENTAL.md) - Frontend interface
- [Edify Configuration Guide](./crates/config/)

---

**Hstudioy Testing! 🚀**