.PHONY: default setup relay auth check fix upgrade build test run clean help

# Default target - show available commands
default: help

# Help target to list available commands
# Help target to list available commands
help:
	@echo "Available targets:"
	@echo ""
	@echo "Development:"
	@echo "  setup      - Install required dependencies"
	@echo "  auth       - Generate authentication keys and tokens"
	@echo "  build      - Build the project"
	@echo "  clean      - Clean build artifacts"
	@echo ""
	@echo "Running servers:"
	@echo "  root       - Run as root node (port 4443)"
	@echo "  leaf       - Run as leaf node (port 4444, connects to root)"
	@echo "  relay      - Run the relay server (alias for root)"
	@echo "  run        - Run the main application (alias for root)"
	@echo "  cluster    - Run both root and leaf nodes in parallel"
	@echo ""
	@echo "Testing:"
	@echo "  test       - Run all tests"
	@echo "  check      - Run CI checks (test, clippy, fmt)"
	@echo "  fix        - Automatically fix code issues"
	@echo "  upgrade    - Upgrade dependencies"
# Install required dependencies
setup:
	cargo install cargo-shear cargo-sort cargo-upgrades cargo-edit cargo-audit

# Generate authentication keys and tokens for development
auth:
	@mkdir -p dev
	@if [ ! -f "dev/root.jwk" ]; then \
		echo "Generating JWK key..."; \
		cargo run --bin moq-token -- --key "dev/root.jwk" generate; \
	fi
	@if [ ! -f "dev/root.jwt" ]; then \
		echo "Generating JWT token..."; \
		cargo run --quiet --bin moq-token -- --key "dev/root.jwk" sign --path "" --subscribe "" --publish "" > dev/root.jwt; \
	fi
	@echo "Authentication files ready in dev/ directory"

# Run the relay server (main edify application) as root node
relay: auth
	CONFIG_FILE=config.toml cargo run --bin edify

# Run as root node (same as relay)
root: relay

# Run as leaf node connecting to root
leaf: auth
	@echo "Starting leaf node on port 4444, connecting to root on port 4443..."
	CONFIG_FILE=config-leaf.toml cargo run --bin edify

# Alias for relay (root node)
run: relay

# Run cluster (root + leaf in parallel) - requires GNU parallel or similar
cluster: auth
	@echo "Starting cluster with root and leaf nodes..."
	@echo "Root node will run on port 4443, leaf node on port 4444"
	@echo "Press Ctrl+C to stop both nodes"
	@command -v parallel >/dev/null 2>&1 || (echo "GNU parallel not found. Install with: sudo apt-get install parallel" && exit 1)
	parallel --line-buffer --halt now,fail=1 ::: \
		"CONFIG_FILE=config.toml cargo run --bin edify" \
		"sleep 2 && CONFIG_FILE=config-leaf.toml cargo run --bin edify"

# Run all tests
test:
	cargo test --all-targets --all-features

# Run tests with output
test-verbose:
	cargo test --all-targets --all-features -- --nocapture

# Run CI checks
check:
	cargo test --all-targets --all-features
	cargo clippy --all-targets --all-features -- -D warnings
	cargo fmt --all --check
	@command -v cargo-shear >/dev/null 2>&1 && cargo shear || echo "Warning: cargo-shear not installed, run 'make setup'"
	@command -v cargo-sort >/dev/null 2>&1 && cargo sort --workspace --check || echo "Warning: cargo-sort not installed, run 'make setup'"
	@command -v cargo-audit >/dev/null 2>&1 && cargo audit || echo "Warning: cargo-audit not installed, run 'make setup'"

# Automatically fix code issues
fix:
	cargo fix --allow-staged --all-targets --all-features
	cargo clippy --fix --allow-staged --all-targets --all-features
	cargo fmt --all
	@command -v cargo-shear >/dev/null 2>&1 && cargo shear --fix || echo "Warning: cargo-shear not installed, run 'make setup'"
	@command -v cargo-sort >/dev/null 2>&1 && cargo sort --workspace || echo "Warning: cargo-sort not installed, run 'make setup'"
	cargo update

# Upgrade dependencies
upgrade:
	cargo update
	@command -v cargo-upgrades >/dev/null 2>&1 && cargo upgrade --incompatible || echo "Warning: cargo-upgrades not installed, run 'make setup'"

# Build the project
build:
	cargo build

# Build release version
build-release:
	cargo build --release

# Clean build artifacts
clean:
	cargo clean
	rm -rf target/

# Development targets
dev-clean: clean auth
	@echo "Development environment cleaned and reset"

# Quick development setup
dev-setup: setup auth build
	@echo "Development environment ready!"

# Run tests for specific crates
test-config:
	cargo test -p config -- --nocapture

test-relay:
	cargo test -p relay -- --nocapture

test-streamhub:
	cargo test -p streamhub -- --nocapture

test-stream-client:
	cargo test -p stream_client -- --nocapture

# Install the binary
install: build-release
	cargo install --path edify

# Development server with auto-restart (requires cargo-watch)
dev-watch:
	@command -v cargo-watch >/dev/null 2>&1 || (echo "cargo-watch not found. Install with: cargo install cargo-watch" && exit 1)
	cargo watch -x "run --bin edify"

# Generate fresh auth tokens (removes existing ones)
auth-fresh:
	rm -f dev/root.jwk dev/root.jwt
	$(MAKE) auth

# Verify token functionality
auth-verify: auth
	@echo "Verifying generated token..."
	@cat dev/root.jwt | cargo run --bin moq-token -- --key "dev/root.jwk" verify

# Show project status
status:
	@echo "=== Project Status ==="
	@echo "Rust version: $$(rustc --version)"
	@echo "Cargo version: $$(cargo --version)"
	@echo "Auth files:"
	@ls -la dev/ 2>/dev/null || echo "  No dev/ directory found"
	@echo "Build status:"
	@cargo check --quiet && echo "  ✓ Project compiles" || echo "  ✗ Compilation errors"

# Cluster management targets
cluster-simple: auth
	@echo "Starting simple cluster (no parallel dependency)..."
	@echo "Run 'make root' in one terminal and 'make leaf' in another"
	@echo "Or install GNU parallel and use 'make cluster'"

# Test cluster connectivity
test-cluster:
	@echo "Testing cluster connectivity..."
	@echo "This will start root node briefly to test configuration"
	timeout 3 make root || echo "Root node configuration tested"

# Stop any running edify processes
stop:
	@echo "Stopping all edify processes..."
	@pkill -f "edify" || echo "No edify processes found"

# Show cluster status
cluster-status:
	@echo "=== Cluster Status ==="
	@echo "Active edify processes:"
	@pgrep -f "edify" -l || echo "No edify processes running"
	@echo ""
	@echo "Port status:"
	@netstat -tuln | grep -E ":(4443|4444|8080|8081)" || echo "No cluster ports active"

# Docker targets (if needed in future)
docker-build:
	docker build -t edify-studio .

docker-run: docker-build
	docker run -p 4443:4443 edify-studio

# Advanced cluster with different terminals (requires tmux)
cluster-tmux: auth
	@command -v tmux >/dev/null 2>&1 || (echo "tmux not found. Install with: sudo apt-get install tmux" && exit 1)
	@echo "Starting cluster in tmux session 'edify-cluster'..."
	tmux new-session -d -s edify-cluster -n root 'make root'
	tmux new-window -t edify-cluster -n leaf 'sleep 3 && make leaf'
	tmux attach-session -t edify-cluster

# Kill tmux cluster session
cluster-tmux-stop:
	tmux kill-session -t edify-cluster 2>/dev/null || echo "No tmux session 'edify-cluster' found"