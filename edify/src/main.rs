use std::sync::Arc;
use tokio::signal;

use relay::RelaySever;
use streamhub::StreamHub;

use crate::rpc::server;

// Analyze the studio/src/routes/(app)/+page.svelte file to understand its current structure, styling patterns, and TailwindCSS usage. Focus on identifying opportunities to replace existing TailwindCSS classes with DaisyUI components and utilities to achieve consistent styling while reducing CSS complexity. Examine the component hierarchy, layout patterns, color schemes, spacing, typography, and interactive elements currently implemented with TailwindCSS. Document specific areas where DaisyUI's pre-built components (buttons, cards, forms, navigation, etc.) can replace custom TailwindCSS combinations. Provide recommendations for consolidating repetitive styling patterns into DaisyUI's semantic component classes while maintaining the existing visual design and functionality. Consider the broader design system implications and ensure the proposed changes align with DaisyUI's theming capabilities and component conventions.

pub mod rpc;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let mut relay_server = RelaySever::new().create().await?;
    let mut hub = StreamHub::new();

    let bind_addr = relay_server.bind_addr();
    let cluster = Arc::new(relay_server.cluster()?.clone());
    let fingerprints = relay_server.fingerprints().to_vec();
    let hub_handle = hub.handle();
    let relay_handle = relay_server.handle();
    
    let (rpc_server, rpc_handle) =
        server::create(bind_addr, hub_handle.clone(), cluster, fingerprints)?;

    let relay_task = tokio::spawn(async move { relay_server.run().await });
    let hub_task = tokio::spawn(async move { hub.run().await });
    let rpc_task = tokio::spawn(async move { rpc_server.run().await });

    // Setup graceful shutdown
    tokio::select! {
        result = relay_task => {
            tracing::info!("Relay task completed: {:?}", result);
        }
        result = hub_task => {
            tracing::info!("Hub task completed: {:?}", result);
        }
        result = rpc_task => {
            tracing::info!("RPC task completed: {:?}", result);
        }
        _ = signal::ctrl_c() => {
            hub_handle.shutdown().await?;
            rpc_handle.shutdown().await?;
            relay_handle.shutdown().await?;
            tracing::info!("Received Ctrl+C, shutting down gracefully...");
        }
    }
    Ok(())
}
