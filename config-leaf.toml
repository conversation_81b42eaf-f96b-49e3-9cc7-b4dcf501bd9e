# Leaf node configuration file for Edify services
# This configuration connects to a root relay server

# Global logging configuration
[log]
level = "info"
verbose = 0

# Relay service configuration
[relay]
# Server configuration
[relay.server]
listen = "[::]:4444"

# Generate a self-signed certificate for the given hostnames.
tls.generate = ["localhost"]

# Client configuration (for clustering)
[relay.client]
# Disable TLS verification for development
tls.disable_verify = true

# Cluster configuration - connects to root node
[relay.cluster]
# Connect to the root hostname to discover other nodes
connect = "localhost:4443"

# Use the token in this file when connecting to other nodes
token = "dev/root.jwt"

# My hostname, which must be accessible from other nodes
advertise = "localhost:4444"

# The prefix to use for cluster announcements
prefix = "internal/origins"

node_id = "leaf-01"
max_streams = 500
node_status_enabled = true

# Authentication configuration
[relay.auth]
# Use the same key as root for simplicity in development
key = "dev/root.jwk"

# Allow any connection to `/demo/**` without a token
path = { demo = "" }

# Hub service configuration
[hub]
enable_hub = false
max_sources = 5
max_destinations = 10
high_water_mark = 1000
auto_cleanup = true
status_port = 8081